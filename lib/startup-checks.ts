/**
 * Startup validation system for PaperNugget
 * Validates environment variables, database connectivity, and schema readiness
 */

import { query } from './db'
import { checkSMTPHealth } from './mailer'
import { config } from './config'
import { quickSchemaValidation } from './schema-validator'

export interface StartupCheck {
  name: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: any
}

export interface StartupValidationResult {
  success: boolean
  checks: StartupCheck[]
  errors: string[]
  warnings: string[]
}

/**
 * Validate all required environment variables
 */
async function validateEnvironmentVariables(): Promise<StartupCheck[]> {
  const checks: StartupCheck[] = []
  
  // Required environment variables
  const requiredVars = [
    { name: 'DATABASE_URL', value: process.env.DATABASE_URL },
    { name: 'APP_URL', value: process.env.APP_URL },
    { name: 'EMAIL_FROM', value: process.env.EMAIL_FROM },
    { name: 'SMTP_HOST', value: process.env.SMTP_HOST },
    { name: 'SMTP_PORT', value: process.env.SMTP_PORT },
  ]

  for (const envVar of requiredVars) {
    if (!envVar.value) {
      checks.push({
        name: `Environment Variable: ${envVar.name}`,
        status: 'fail',
        message: `Required environment variable ${envVar.name} is not set`,
      })
    } else {
      checks.push({
        name: `Environment Variable: ${envVar.name}`,
        status: 'pass',
        message: `${envVar.name} is configured`,
        details: envVar.name.includes('PASSWORD') || envVar.name.includes('SECRET') 
          ? '[REDACTED]' 
          : envVar.value,
      })
    }
  }

  // Optional but recommended variables
  const optionalVars = [
    { name: 'SMTP_USER', value: process.env.SMTP_USER },
    { name: 'SMTP_PASS', value: process.env.SMTP_PASS },
    { name: 'JWT_SECRET', value: process.env.JWT_SECRET },
  ]

  for (const envVar of optionalVars) {
    if (!envVar.value) {
      checks.push({
        name: `Environment Variable: ${envVar.name}`,
        status: 'warning',
        message: `Optional environment variable ${envVar.name} is not set`,
      })
    } else {
      checks.push({
        name: `Environment Variable: ${envVar.name}`,
        status: 'pass',
        message: `${envVar.name} is configured`,
        details: '[REDACTED]',
      })
    }
  }

  return checks
}

/**
 * Validate database connectivity and basic functionality
 */
async function validateDatabaseConnectivity(): Promise<StartupCheck[]> {
  const checks: StartupCheck[] = []

  try {
    // Test basic connectivity
    const start = Date.now()
    await query('SELECT 1 as test')
    const responseTime = Date.now() - start

    checks.push({
      name: 'Database Connectivity',
      status: 'pass',
      message: 'Database connection successful',
      details: { responseTime: `${responseTime}ms` },
    })

    // Test database version
    try {
      const versionResult = await query('SELECT version()')
      const version = versionResult.rows[0]?.version || 'Unknown'
      
      checks.push({
        name: 'Database Version',
        status: 'pass',
        message: 'Database version retrieved',
        details: { version: version.split(' ').slice(0, 2).join(' ') },
      })
    } catch (error) {
      checks.push({
        name: 'Database Version',
        status: 'warning',
        message: 'Could not retrieve database version',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      })
    }

  } catch (error) {
    checks.push({
      name: 'Database Connectivity',
      status: 'fail',
      message: 'Database connection failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
    })
  }

  return checks
}

/**
 * Validate database schema readiness
 */
async function validateDatabaseSchema(): Promise<StartupCheck[]> {
  const checks: StartupCheck[] = []

  // Required tables
  const requiredTables = [
    'users',
    'user_sessions',
    'password_reset_tokens',
    'email_verification_tokens',
    'papers',
    'notes',
    'collections',
    'reviews',
    'audit_logs'
  ]

  for (const tableName of requiredTables) {
    try {
      const result = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = $1
        );
      `, [tableName])

      if (result.rows[0].exists) {
        // Check if table has data (for some tables)
        if (['users'].includes(tableName)) {
          const countResult = await query(`SELECT COUNT(*) as count FROM ${tableName}`)
          const count = parseInt(countResult.rows[0].count)
          
          checks.push({
            name: `Database Table: ${tableName}`,
            status: 'pass',
            message: `Table ${tableName} exists and has ${count} records`,
            details: { recordCount: count },
          })
        } else {
          checks.push({
            name: `Database Table: ${tableName}`,
            status: 'pass',
            message: `Table ${tableName} exists`,
          })
        }
      } else {
        checks.push({
          name: `Database Table: ${tableName}`,
          status: 'fail',
          message: `Required table ${tableName} does not exist`,
        })
      }
    } catch (error) {
      checks.push({
        name: `Database Table: ${tableName}`,
        status: 'fail',
        message: `Error checking table ${tableName}`,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      })
    }
  }

  // Run comprehensive schema validation
  try {
    const schemaValidation = await quickSchemaValidation()

    if (schemaValidation.isValid) {
      checks.push({
        name: 'Database Schema Validation',
        status: 'pass',
        message: 'Database schema is valid and complete',
        details: { validationPassed: true }
      })
    } else {
      checks.push({
        name: 'Database Schema Validation',
        status: 'fail',
        message: 'Database schema validation failed',
        details: {
          validationPassed: false,
          errors: schemaValidation.errors
        }
      })
    }
  } catch (error) {
    checks.push({
      name: 'Database Schema Validation',
      status: 'warning',
      message: 'Could not run schema validation',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    })
  }

  return checks
}

/**
 * Validate email system functionality
 */
async function validateEmailSystem(): Promise<StartupCheck[]> {
  const checks: StartupCheck[] = []

  try {
    const start = Date.now()
    const smtpResult = await checkSMTPHealth()
    const responseTime = Date.now() - start

    if (smtpResult.healthy) {
      checks.push({
        name: 'SMTP Connectivity',
        status: 'pass',
        message: 'SMTP server connection successful',
        details: { responseTime: `${responseTime}ms` },
      })
    } else {
      checks.push({
        name: 'SMTP Connectivity',
        status: 'fail',
        message: 'SMTP server connection failed',
        details: { error: smtpResult.error, responseTime: `${responseTime}ms` },
      })
    }
  } catch (error) {
    checks.push({
      name: 'SMTP Connectivity',
      status: 'fail',
      message: 'SMTP health check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
    })
  }

  // Validate email configuration
  try {
    const emailConfig = {
      from: config.email.emailFrom,
      host: config.email.smtpHost,
      port: config.email.smtpPort,
      tls: config.email.smtpTls,
    }

    checks.push({
      name: 'Email Configuration',
      status: 'pass',
      message: 'Email configuration loaded successfully',
      details: emailConfig,
    })
  } catch (error) {
    checks.push({
      name: 'Email Configuration',
      status: 'fail',
      message: 'Email configuration validation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
    })
  }

  return checks
}

/**
 * Run all startup validation checks
 */
export async function runStartupValidation(): Promise<StartupValidationResult> {
  console.log('🔍 Running startup validation checks...')
  
  const allChecks: StartupCheck[] = []
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // Run all validation checks
    const envChecks = await validateEnvironmentVariables()
    const dbConnectivityChecks = await validateDatabaseConnectivity()
    const dbSchemaChecks = await validateDatabaseSchema()
    const emailChecks = await validateEmailSystem()

    allChecks.push(...envChecks, ...dbConnectivityChecks, ...dbSchemaChecks, ...emailChecks)

    // Collect errors and warnings
    for (const check of allChecks) {
      if (check.status === 'fail') {
        errors.push(`${check.name}: ${check.message}`)
      } else if (check.status === 'warning') {
        warnings.push(`${check.name}: ${check.message}`)
      }
    }

    const success = errors.length === 0

    // Log results
    console.log(`✅ Startup validation completed: ${allChecks.length} checks run`)
    console.log(`   - Passed: ${allChecks.filter(c => c.status === 'pass').length}`)
    console.log(`   - Warnings: ${warnings.length}`)
    console.log(`   - Errors: ${errors.length}`)

    if (errors.length > 0) {
      console.log('❌ Startup validation failed with errors:')
      errors.forEach(error => console.log(`   - ${error}`))
    }

    if (warnings.length > 0) {
      console.log('⚠️  Startup validation warnings:')
      warnings.forEach(warning => console.log(`   - ${warning}`))
    }

    return {
      success,
      checks: allChecks,
      errors,
      warnings,
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('❌ Startup validation failed:', errorMessage)
    
    return {
      success: false,
      checks: allChecks,
      errors: [`Startup validation failed: ${errorMessage}`],
      warnings,
    }
  }
}

/**
 * Get a summary of the current system health
 */
export async function getSystemHealthSummary() {
  const validation = await runStartupValidation()
  
  return {
    healthy: validation.success,
    totalChecks: validation.checks.length,
    passed: validation.checks.filter(c => c.status === 'pass').length,
    warnings: validation.warnings.length,
    errors: validation.errors.length,
    lastChecked: new Date().toISOString(),
  }
}
