/**
 * Simple in-memory cache for API responses
 * Used to minimize Zotero API calls and improve performance
 */

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>()

  /**
   * Get cached data if it exists and hasn't expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) {
      return null
    }

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      // Expired - remove from cache
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  /**
   * Set cached data with TTL
   */
  set<T>(key: string, data: T, ttlMs: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    })
  }

  /**
   * Remove specific key from cache
   */
  delete(key: string): void {
    this.cache.delete(key)
  }

  /**
   * Clear all cached data
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// Global cache instance
const cache = new MemoryCache()

// Cache TTL constants
export const CACHE_TTL = {
  GROUPS_LIST: 30 * 60 * 1000, // 30 minutes
  SEARCH_RESULT: 5 * 60 * 1000, // 5 minutes for search results
  USER_LIBRARIES: 30 * 60 * 1000, // 30 minutes
} as const

/**
 * Cache key generators
 */
export const CacheKeys = {
  zoteroGroups: (userId: string) => `zotero:groups:${userId}`,
  zoteroLibraries: (userId: string) => `zotero:libraries:${userId}`,
  zoteroSearch: (userId: string, paperId: string, libraryType: string, libraryId: string) => 
    `zotero:search:${userId}:${paperId}:${libraryType}:${libraryId}`,
  zoteroSearchByDoi: (userId: string, doi: string, libraryType: string, libraryId: string) =>
    `zotero:search:doi:${userId}:${doi}:${libraryType}:${libraryId}`,
  zoteroSearchByTitle: (userId: string, title: string, year: number | undefined, libraryType: string, libraryId: string) =>
    `zotero:search:title:${userId}:${title}:${year || 'no-year'}:${libraryType}:${libraryId}`,
} as const

/**
 * Cached wrapper for Zotero groups API
 */
export async function getCachedZoteroGroups<T>(
  userId: string,
  fetchFn: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.zoteroGroups(userId)
  
  // Try to get from cache first
  const cached = cache.get<T>(cacheKey)
  if (cached !== null) {
    console.log('Cache hit for Zotero groups', { userId, cacheKey })
    return cached
  }

  // Cache miss - fetch from API
  console.log('Cache miss for Zotero groups - fetching from API', { userId, cacheKey })
  const data = await fetchFn()
  
  // Cache the result
  cache.set(cacheKey, data, CACHE_TTL.GROUPS_LIST)
  
  return data
}

/**
 * Cached wrapper for Zotero libraries API
 */
export async function getCachedZoteroLibraries<T>(
  userId: string,
  fetchFn: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.zoteroLibraries(userId)
  
  // Try to get from cache first
  const cached = cache.get<T>(cacheKey)
  if (cached !== null) {
    console.log('Cache hit for Zotero libraries', { userId, cacheKey })
    return cached
  }

  // Cache miss - fetch from API
  console.log('Cache miss for Zotero libraries - fetching from API', { userId, cacheKey })
  const data = await fetchFn()
  
  // Cache the result
  cache.set(cacheKey, data, CACHE_TTL.USER_LIBRARIES)
  
  return data
}

/**
 * Cached wrapper for Zotero search results
 */
export async function getCachedZoteroSearch<T>(
  userId: string,
  searchKey: string,
  fetchFn: () => Promise<T>
): Promise<T> {
  // Try to get from cache first
  const cached = cache.get<T>(searchKey)
  if (cached !== null) {
    console.log('Cache hit for Zotero search', { userId, searchKey })
    return cached
  }

  // Cache miss - fetch from API
  console.log('Cache miss for Zotero search - fetching from API', { userId, searchKey })
  const data = await fetchFn()
  
  // Cache the result for shorter time (search results can change)
  cache.set(searchKey, data, CACHE_TTL.SEARCH_RESULT)
  
  return data
}

/**
 * Invalidate cache entries for a user
 */
export function invalidateUserCache(userId: string): void {
  const keysToDelete = [
    CacheKeys.zoteroGroups(userId),
    CacheKeys.zoteroLibraries(userId)
  ]
  
  for (const key of keysToDelete) {
    cache.delete(key)
  }
  
  console.log('Invalidated cache for user', { userId, keysDeleted: keysToDelete.length })
}

/**
 * Invalidate search cache for a specific paper
 */
export function invalidatePaperSearchCache(userId: string, paperId: string): void {
  const stats = cache.getStats()
  const searchKeys = stats.keys.filter(key => 
    key.includes(`zotero:search:${userId}:${paperId}:`) ||
    key.includes(`zotero:search:doi:${userId}:`) ||
    key.includes(`zotero:search:title:${userId}:`)
  )
  
  for (const key of searchKeys) {
    cache.delete(key)
  }
  
  console.log('Invalidated search cache for paper', { userId, paperId, keysDeleted: searchKeys.length })
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  return cache.getStats()
}

/**
 * Clean up expired cache entries
 */
export function cleanupCache(): void {
  cache.cleanup()
}

// Set up periodic cleanup (every 10 minutes)
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupCache, 10 * 60 * 1000)
}

export default cache
