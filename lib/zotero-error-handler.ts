/**
 * Comprehensive error handling for Zotero sync operations
 * Provides standardized error messages and retry logic
 */

export interface ZoteroErrorDetails {
  type: 'auth' | 'rate_limit' | 'permissions' | 'invalid_doi' | 'network' | 'api' | 'unknown'
  message: string
  userMessage: string
  retryable: boolean
  retryAfter?: number // seconds
  suggestedAction?: string
}

export interface RetryConfig {
  maxRetries: number
  baseDelay: number // milliseconds
  maxDelay: number // milliseconds
  backoffMultiplier: number
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2
}

/**
 * Parse Zotero API error and return structured error details
 */
export function parseZoteroError(error: any): ZoteroErrorDetails {
  const errorMessage = error?.message || error?.toString() || 'Unknown error'
  const statusCode = error?.status || error?.statusCode

  // Authentication errors
  if (statusCode === 401 || errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
    return {
      type: 'auth',
      message: errorMessage,
      userMessage: 'Authentication failed. Please check your Zotero API key.',
      retryable: false,
      suggestedAction: 'Go to Settings → Zotero Integration and verify your API key is correct and has the necessary permissions.'
    }
  }

  // Rate limiting
  if (statusCode === 429 || errorMessage.includes('429') || errorMessage.includes('rate limit')) {
    const retryAfter = extractRetryAfter(error) || 60 // Default to 60 seconds
    return {
      type: 'rate_limit',
      message: errorMessage,
      userMessage: 'Zotero API rate limit exceeded. Please wait before trying again.',
      retryable: true,
      retryAfter,
      suggestedAction: `Wait ${retryAfter} seconds before retrying. Consider reducing the frequency of sync operations.`
    }
  }

  // Permission errors
  if (statusCode === 403 || errorMessage.includes('403') || errorMessage.includes('Forbidden') || 
      errorMessage.includes('Cannot edit item') || errorMessage.includes('insufficient permissions')) {
    return {
      type: 'permissions',
      message: errorMessage,
      userMessage: 'Insufficient permissions to access this Zotero resource.',
      retryable: false,
      suggestedAction: 'Check that your API key has write permissions for the target library or group.'
    }
  }

  // Invalid DOI
  if (errorMessage.includes('Invalid DOI') || errorMessage.includes('DOI not found') || 
      errorMessage.includes('malformed DOI')) {
    return {
      type: 'invalid_doi',
      message: errorMessage,
      userMessage: 'The paper\'s DOI is invalid or not recognized.',
      retryable: false,
      suggestedAction: 'Try syncing by title instead, or verify the DOI is correct.'
    }
  }

  // Network errors
  if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('ENOTFOUND') || 
      errorMessage.includes('ETIMEDOUT') || errorMessage.includes('network') ||
      statusCode >= 500) {
    return {
      type: 'network',
      message: errorMessage,
      userMessage: 'Network error occurred while connecting to Zotero.',
      retryable: true,
      suggestedAction: 'Check your internet connection and try again. If the problem persists, Zotero\'s servers may be temporarily unavailable.'
    }
  }

  // API errors (4xx)
  if (statusCode >= 400 && statusCode < 500) {
    return {
      type: 'api',
      message: errorMessage,
      userMessage: 'Zotero API request failed.',
      retryable: false,
      suggestedAction: 'The request was invalid. Please check your settings and try again.'
    }
  }

  // Unknown errors
  return {
    type: 'unknown',
    message: errorMessage,
    userMessage: 'An unexpected error occurred during Zotero sync.',
    retryable: true,
    suggestedAction: 'Try again. If the problem persists, please contact support.'
  }
}

/**
 * Extract retry-after header value from error
 */
function extractRetryAfter(error: any): number | undefined {
  const retryAfter = error?.headers?.['retry-after'] || error?.retryAfter
  if (retryAfter) {
    const seconds = parseInt(retryAfter, 10)
    return isNaN(seconds) ? undefined : seconds
  }
  return undefined
}

/**
 * Calculate delay for exponential backoff
 */
export function calculateBackoffDelay(attempt: number, config: RetryConfig = DEFAULT_RETRY_CONFIG): number {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
  return Math.min(delay, config.maxDelay)
}

/**
 * Retry wrapper with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  onRetry?: (attempt: number, error: ZoteroErrorDetails) => void
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      const errorDetails = parseZoteroError(error)

      // Don't retry if error is not retryable
      if (!errorDetails.retryable) {
        throw error
      }

      // Don't retry if we've exhausted attempts
      if (attempt > config.maxRetries) {
        throw error
      }

      // Calculate delay
      let delay = calculateBackoffDelay(attempt, config)
      
      // Use retry-after header if available for rate limits
      if (errorDetails.type === 'rate_limit' && errorDetails.retryAfter) {
        delay = errorDetails.retryAfter * 1000 // Convert to milliseconds
      }

      console.log(`Retry attempt ${attempt}/${config.maxRetries} after ${delay}ms`, {
        error: errorDetails.message,
        type: errorDetails.type
      })

      // Call retry callback if provided
      if (onRetry) {
        onRetry(attempt, errorDetails)
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

/**
 * Format error for user display
 */
export function formatErrorForUser(error: any): { title: string; description: string; action?: string } {
  const errorDetails = parseZoteroError(error)

  return {
    title: getErrorTitle(errorDetails.type),
    description: errorDetails.userMessage,
    action: errorDetails.suggestedAction
  }
}

/**
 * Get user-friendly error title
 */
function getErrorTitle(errorType: ZoteroErrorDetails['type']): string {
  switch (errorType) {
    case 'auth':
      return 'Authentication Failed'
    case 'rate_limit':
      return 'Rate Limit Exceeded'
    case 'permissions':
      return 'Permission Denied'
    case 'invalid_doi':
      return 'Invalid DOI'
    case 'network':
      return 'Network Error'
    case 'api':
      return 'API Error'
    default:
      return 'Sync Failed'
  }
}

/**
 * Log structured error for debugging
 */
export function logZoteroError(
  error: any, 
  context: { 
    operation: string
    paperId?: string
    userId?: string
    libraryType?: string
    libraryId?: string
  }
): void {
  const errorDetails = parseZoteroError(error)
  
  console.error('Zotero sync error', {
    ...context,
    error: {
      type: errorDetails.type,
      message: errorDetails.message,
      retryable: errorDetails.retryable,
      retryAfter: errorDetails.retryAfter
    },
    timestamp: new Date().toISOString()
  })
}
