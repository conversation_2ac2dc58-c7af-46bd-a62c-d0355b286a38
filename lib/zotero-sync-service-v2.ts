/**
 * Zotero Sync Service V2 - Simplified sync flow
 * 
 * This service implements the new simplified Zotero sync flow:
 * 1. Short-circuit if paper already has mapping (zoteroItemKey + zoteroGroupId)
 * 2. Search in default group by DOI then title
 * 3. Prompt user for action if not found in default group
 * 4. Support manual group selection and creation
 */

import { ZoteroClient } from './zotero-client'
import { papers, notes } from './database'
import { Paper, Note, ZoteroSyncResult, ZoteroSettings } from './types'
import { getCachedZoteroLibraries, getCachedZoteroSearch, CacheKeys } from './cache'
import { withRetry, parseZoteroError, logZoteroError, formatErrorForUser } from './zotero-error-handler'

export interface ZoteroSyncTelemetry {
  startTime: number
  endTime: number
  duration: number
  apiCalls: number
  cacheHits: number
  searchAttempts: number
}

export interface ZoteroSyncResultV2 extends ZoteroSyncResult {
  telemetry?: ZoteroSyncTelemetry
  requiresUserSelection?: boolean
  availableGroups?: Array<{ id: string; name: string; type: string }>
  searchedInDefaultGroup?: boolean
  errorType?: 'auth' | 'rate_limit' | 'permissions' | 'invalid_doi' | 'network' | 'api' | 'unknown'
  retryable?: boolean
}

export class ZoteroSyncServiceV2 {
  private client: ZoteroClient
  private settings: ZoteroSettings
  private userId: string
  private telemetry: Partial<ZoteroSyncTelemetry> = {}

  constructor(apiKey: string, settings: ZoteroSettings, userId: string) {
    this.client = new ZoteroClient(apiKey)
    this.settings = settings
    this.userId = userId
  }

  /**
   * Main sync method - implements the new simplified flow
   */
  async syncPaper(paperId: string): Promise<ZoteroSyncResultV2> {
    // New sync flow is now the default - no feature flag check needed

    this.telemetry = {
      startTime: Date.now(),
      apiCalls: 0,
      cacheHits: 0,
      searchAttempts: 0
    }

    try {
      // Step 1: Short-circuit if paper already has mapping
      const shortCircuitResult = await this.tryShortCircuit(paperId)
      if (shortCircuitResult.success) {
        return this.finalizeTelemetry(shortCircuitResult)
      }

      // Step 2: Search in default group
      const defaultGroupResult = await this.searchInDefaultGroup(paperId)
      if (defaultGroupResult.success) {
        return this.finalizeTelemetry(defaultGroupResult)
      }

      // Step 3: Return "requires user selection" status
      return this.finalizeTelemetry({
        success: false,
        requiresUserSelection: true,
        searchedInDefaultGroup: true,
        error: 'Paper not found in default group - user selection required'
      })

    } catch (error) {
      // Log structured error for debugging
      logZoteroError(error, {
        operation: 'syncPaper',
        paperId,
        userId: this.userId
      })

      // Parse error for user-friendly message
      const errorDetails = parseZoteroError(error)

      return this.finalizeTelemetry({
        success: false,
        error: errorDetails.userMessage,
        errorType: errorDetails.type,
        retryable: errorDetails.retryable
      })
    }
  }

  /**
   * Step 1: Short-circuit if paper already has mapping
   */
  private async tryShortCircuit(paperId: string): Promise<ZoteroSyncResultV2> {
    const paper = await papers.getById(paperId)
    if (!paper) {
      return { success: false, error: 'Paper not found' }
    }

    // Check if paper has both item key and group ID (indicating existing mapping)
    if (paper.zoteroItemKey && paper.zoteroGroupId !== undefined) {
      console.log('Short-circuiting sync - paper already has mapping', {
        paperId,
        itemKey: paper.zoteroItemKey,
        groupId: paper.zoteroGroupId
      })

      // Sync notes to existing item
      const note = await notes.getByPaperId(paperId)
      if (!note) {
        return { success: false, error: 'Note not found for paper' }
      }

      // Determine library type and ID from stored groupId
      const libraryType = paper.zoteroGroupId ? 'group' : 'user'
      const libraryId = paper.zoteroGroupId || undefined

      this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
      const noteResult = await withRetry(
        () => this.client.createOrUpdateNote(
          libraryType,
          libraryId || '',
          paper.zoteroItemKey,
          note,
          paper.zoteroNoteKey
        ),
        undefined, // Use default retry config
        (attempt, errorDetails) => {
          console.log(`Retrying note sync (attempt ${attempt})`, {
            paperId,
            itemKey: paper.zoteroItemKey,
            errorType: errorDetails.type
          })
        }
      )

      if (!noteResult.success) {
        return { success: false, error: noteResult.error }
      }

      // Update sync timestamp
      await papers.update(paperId, {
        zoteroLastSynced: new Date().toISOString(),
        zoteroSyncStatus: 'synced'
      })

      this.telemetry.cacheHits = 1

      return {
        success: true,
        itemKey: paper.zoteroItemKey,
        noteKey: noteResult.noteKey!,
        message: `Synced to existing ${libraryType === 'user' ? 'personal library' : 'group'} item`
      }
    }

    return { success: false, error: 'No existing mapping found' }
  }

  /**
   * Step 2: Search in default group
   */
  private async searchInDefaultGroup(paperId: string): Promise<ZoteroSyncResultV2> {
    const paper = await papers.getById(paperId)
    if (!paper) {
      return { success: false, error: 'Paper not found' }
    }

    const note = await notes.getByPaperId(paperId)
    if (!note) {
      return { success: false, error: 'Note not found for paper' }
    }

    // Use default group from settings (user library if not specified)
    const defaultLibraryType = this.settings.libraryType || 'user'
    const defaultLibraryId = this.settings.libraryId

    console.log('Searching in default group', {
      paperId,
      libraryType: defaultLibraryType,
      libraryId: defaultLibraryId,
      doi: paper.doi,
      title: paper.title
    })

    this.telemetry.searchAttempts = (this.telemetry.searchAttempts || 0) + 1

    // Try to find existing item by DOI first, then by title (with caching)
    let existingItem = null

    if (paper.doi) {
      const doiCacheKey = CacheKeys.zoteroSearchByDoi(this.userId, paper.doi, defaultLibraryType, defaultLibraryId || '')
      existingItem = await getCachedZoteroSearch(
        this.userId,
        doiCacheKey,
        async () => {
          this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
          return await this.client.findItemByDOI(defaultLibraryType, defaultLibraryId || '', paper.doi!)
        }
      )
    }

    if (!existingItem && paper.title) {
      const titleCacheKey = CacheKeys.zoteroSearchByTitle(this.userId, paper.title, paper.year, defaultLibraryType, defaultLibraryId || '')
      existingItem = await getCachedZoteroSearch(
        this.userId,
        titleCacheKey,
        async () => {
          this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
          return await this.client.findItemByTitle(defaultLibraryType, defaultLibraryId || '', paper.title!, paper.year)
        }
      )
    }

    if (existingItem) {
      // Found existing item - create mapping and sync notes
      console.log('Found existing item in default group', {
        paperId,
        itemKey: existingItem.key,
        libraryType: defaultLibraryType,
        libraryId: defaultLibraryId
      })

      // Create or update note
      this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
      const noteResult = await this.client.createOrUpdateNote(
        defaultLibraryType,
        defaultLibraryId || '',
        existingItem.key,
        note,
        paper.zoteroNoteKey
      )

      if (!noteResult.success) {
        return { success: false, error: noteResult.error }
      }

      // Store mapping in database
      await papers.update(paperId, {
        zoteroItemKey: existingItem.key,
        zoteroNoteKey: noteResult.noteKey!,
        zoteroGroupId: defaultLibraryType === 'group' ? defaultLibraryId : null,
        zoteroLastSynced: new Date().toISOString(),
        zoteroSyncStatus: 'mapped'
      })

      return {
        success: true,
        itemKey: existingItem.key,
        noteKey: noteResult.noteKey!,
        message: `Found and synced to existing item in ${defaultLibraryType === 'user' ? 'personal library' : 'default group'}`
      }
    }

    return { success: false, error: 'Paper not found in default group' }
  }

  /**
   * Create paper in specified group (used after user selection)
   */
  async createInGroup(paperId: string, libraryType: 'user' | 'group', libraryId?: string): Promise<ZoteroSyncResultV2> {
    this.telemetry = {
      startTime: Date.now(),
      apiCalls: 0,
      cacheHits: 0,
      searchAttempts: 0
    }

    try {
      const paper = await papers.getById(paperId)
      if (!paper) {
        return { success: false, error: 'Paper not found' }
      }

      const note = await notes.getByPaperId(paperId)
      if (!note) {
        return { success: false, error: 'Note not found for paper' }
      }

      // Check if paper already has mapping to this library (idempotency check)
      const expectedGroupId = libraryType === 'group' ? libraryId : null
      if (paper.zoteroItemKey && paper.zoteroGroupId === expectedGroupId) {
        console.log('Paper already has mapping to target library, syncing notes only', {
          paperId,
          itemKey: paper.zoteroItemKey,
          groupId: expectedGroupId
        })

        // Just sync notes to existing item
        this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
        const noteResult = await this.client.createOrUpdateNote(
          libraryType,
          libraryId || '',
          paper.zoteroItemKey,
          note,
          paper.zoteroNoteKey
        )

        if (!noteResult.success) {
          return this.finalizeTelemetry({ success: false, error: noteResult.error })
        }

        // Update sync timestamp
        await papers.update(paperId, {
          zoteroLastSynced: new Date().toISOString(),
          zoteroSyncStatus: 'synced'
        })

        this.telemetry.cacheHits = 1

        return this.finalizeTelemetry({
          success: true,
          itemKey: paper.zoteroItemKey,
          noteKey: noteResult.noteKey!,
          message: `Updated existing item in ${libraryType === 'user' ? 'personal library' : 'group'}`
        })
      }

      // Create new item
      this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
      const itemResult = await this.client.createOrUpdateItem(
        libraryType,
        libraryId || '',
        paper
      )

      if (!itemResult.success) {
        return this.finalizeTelemetry({ success: false, error: itemResult.error })
      }

      // Create note
      this.telemetry.apiCalls = (this.telemetry.apiCalls || 0) + 1
      const noteResult = await this.client.createOrUpdateNote(
        libraryType,
        libraryId || '',
        itemResult.itemKey!,
        note
      )

      if (!noteResult.success) {
        return this.finalizeTelemetry({ success: false, error: noteResult.error })
      }

      // Store mapping
      await papers.update(paperId, {
        zoteroItemKey: itemResult.itemKey!,
        zoteroNoteKey: noteResult.noteKey!,
        zoteroGroupId: libraryType === 'group' ? libraryId : null,
        zoteroLastSynced: new Date().toISOString(),
        zoteroSyncStatus: 'synced'
      })

      return this.finalizeTelemetry({
        success: true,
        itemKey: itemResult.itemKey!,
        noteKey: noteResult.noteKey!,
        message: `Created new item in ${libraryType === 'user' ? 'personal library' : 'group'}`
      })

    } catch (error) {
      return this.finalizeTelemetry({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  /**
   * Get available groups for user selection (with caching)
   */
  async getAvailableGroups(): Promise<Array<{ id: string; name: string; type: string }>> {
    try {
      const libraries = await getCachedZoteroLibraries(
        this.userId,
        async () => await this.client.getUserLibraries()
      )

      return libraries
        .filter(lib => lib.type === 'group')
        .map(lib => ({
          id: lib.id,
          name: lib.name,
          type: lib.type
        }))
    } catch (error) {
      console.error('Error fetching available groups:', error)
      return []
    }
  }

  /**
   * Finalize telemetry and return result
   */
  private finalizeTelemetry(result: ZoteroSyncResultV2): ZoteroSyncResultV2 {
    this.telemetry.endTime = Date.now()
    this.telemetry.duration = this.telemetry.endTime - (this.telemetry.startTime || 0)

    return {
      ...result,
      telemetry: this.telemetry as ZoteroSyncTelemetry
    }
  }
}
