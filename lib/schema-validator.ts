/**
 * Database Schema Validation Utility
 * Validates that the database schema matches the expected structure
 */

import { query } from './db'

export interface SchemaValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  details: {
    tables: TableValidationResult[]
    indexes: IndexValidationResult[]
    constraints: ConstraintValidationResult[]
  }
}

export interface TableValidationResult {
  tableName: string
  exists: boolean
  columns: ColumnValidationResult[]
  missingColumns: string[]
  extraColumns: string[]
}

export interface ColumnValidationResult {
  columnName: string
  exists: boolean
  dataType: string
  expectedDataType: string
  isNullable: boolean
  expectedNullable: boolean
  hasDefault: boolean
  expectedDefault: boolean
}

export interface IndexValidationResult {
  indexName: string
  exists: boolean
  tableName: string
}

export interface ConstraintValidationResult {
  constraintName: string
  exists: boolean
  tableName: string
  constraintType: string
}

// Expected schema definition
const EXPECTED_SCHEMA = {
  tables: {
    users: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        email: { type: 'character varying', nullable: false, hasDefault: false },
        password_hash: { type: 'character varying', nullable: false, hasDefault: false },
        display_name: { type: 'character varying', nullable: true, hasDefault: false },
        role: { type: 'character varying', nullable: true, hasDefault: true },
        email_verified: { type: 'boolean', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        updated_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        last_login: { type: 'timestamp with time zone', nullable: true, hasDefault: false },
        is_active: { type: 'boolean', nullable: true, hasDefault: true },
        privacy_settings: { type: 'jsonb', nullable: true, hasDefault: true },
        preferences: { type: 'jsonb', nullable: true, hasDefault: true }
      }
    },
    papers: {
      columns: {
        id: { type: 'character varying', nullable: false, hasDefault: false },
        title: { type: 'text', nullable: false, hasDefault: false },
        authors: { type: 'ARRAY', nullable: true, hasDefault: true },
        venue: { type: 'character varying', nullable: true, hasDefault: false },
        year: { type: 'integer', nullable: true, hasDefault: false },
        doi: { type: 'character varying', nullable: true, hasDefault: false },
        url: { type: 'text', nullable: true, hasDefault: false },
        abstract: { type: 'text', nullable: true, hasDefault: false },
        citation_count: { type: 'integer', nullable: true, hasDefault: false },
        reference_count: { type: 'integer', nullable: true, hasDefault: false },
        publication_date: { type: 'date', nullable: true, hasDefault: false },
        journal: { type: 'character varying', nullable: true, hasDefault: false },
        volume: { type: 'character varying', nullable: true, hasDefault: false },
        issue: { type: 'character varying', nullable: true, hasDefault: false },
        pages: { type: 'character varying', nullable: true, hasDefault: false },
        tags: { type: 'ARRAY', nullable: true, hasDefault: true },
        starred: { type: 'boolean', nullable: true, hasDefault: true },
        paper_type: { type: 'character varying', nullable: true, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        updated_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        zotero_item_key: { type: 'character varying', nullable: true, hasDefault: false },
        zotero_note_key: { type: 'character varying', nullable: true, hasDefault: false },
        zotero_group_id: { type: 'character varying', nullable: true, hasDefault: false },
        zotero_last_synced: { type: 'timestamp with time zone', nullable: true, hasDefault: false },
        zotero_sync_status: { type: 'character varying', nullable: true, hasDefault: true }
      }
    },
    notes: {
      columns: {
        id: { type: 'character varying', nullable: false, hasDefault: false },
        paper_id: { type: 'character varying', nullable: false, hasDefault: false },
        quick_summary: { type: 'text', nullable: true, hasDefault: false },
        key_ideas: { type: 'ARRAY', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        updated_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    collections: {
      columns: {
        id: { type: 'character varying', nullable: false, hasDefault: false },
        name: { type: 'character varying', nullable: false, hasDefault: false },
        paper_ids: { type: 'ARRAY', nullable: true, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        updated_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    reviews: {
      columns: {
        paper_id: { type: 'character varying', nullable: false, hasDefault: false },
        ease: { type: 'numeric', nullable: true, hasDefault: true },
        next_due: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        last_interval: { type: 'integer', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        updated_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    user_sessions: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        token_hash: { type: 'character varying', nullable: false, hasDefault: false },
        expires_at: { type: 'timestamp with time zone', nullable: false, hasDefault: false },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true },
        ip_address: { type: 'inet', nullable: true, hasDefault: false },
        user_agent: { type: 'text', nullable: true, hasDefault: false }
      }
    },
    password_reset_tokens: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        token_hash: { type: 'character varying', nullable: false, hasDefault: false },
        expires_at: { type: 'timestamp with time zone', nullable: false, hasDefault: false },
        used: { type: 'boolean', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    email_verification_tokens: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        token_hash: { type: 'character varying', nullable: false, hasDefault: false },
        expires_at: { type: 'timestamp with time zone', nullable: false, hasDefault: false },
        used: { type: 'boolean', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    password_history: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        user_id: { type: 'uuid', nullable: false, hasDefault: false },
        password_hash: { type: 'character varying', nullable: false, hasDefault: false },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    },
    audit_logs: {
      columns: {
        id: { type: 'uuid', nullable: false, hasDefault: true },
        user_id: { type: 'uuid', nullable: true, hasDefault: false },
        action: { type: 'character varying', nullable: false, hasDefault: false },
        resource_type: { type: 'character varying', nullable: true, hasDefault: false },
        resource_id: { type: 'character varying', nullable: true, hasDefault: false },
        ip_address: { type: 'inet', nullable: true, hasDefault: false },
        user_agent: { type: 'text', nullable: true, hasDefault: false },
        details: { type: 'jsonb', nullable: true, hasDefault: true },
        created_at: { type: 'timestamp with time zone', nullable: true, hasDefault: true }
      }
    }
  },
  indexes: [
    'idx_users_email',
    'idx_users_role',
    'idx_users_email_verified',
    'idx_users_is_active',
    'idx_users_created_at',
    'idx_user_sessions_user_id',
    'idx_user_sessions_token_hash',
    'idx_user_sessions_expires_at',
    'idx_password_reset_tokens_user_id',
    'idx_password_reset_tokens_token_hash',
    'idx_password_reset_tokens_expires_at',
    'idx_email_verification_tokens_user_id',
    'idx_email_verification_tokens_token_hash',
    'idx_email_verification_tokens_expires_at',
    'idx_audit_logs_user_id',
    'idx_audit_logs_action',
    'idx_audit_logs_resource_type',
    'idx_audit_logs_created_at',
    'idx_papers_user_id',
    'idx_papers_starred',
    'idx_papers_year',
    'idx_papers_created_at',
    'idx_papers_tags',
    'idx_papers_doi',
    'idx_papers_venue',
    'idx_papers_zotero_item_key',
    'idx_papers_zotero_group_id',
    'idx_papers_zotero_sync_status',
    'idx_papers_zotero_last_synced',
    'idx_notes_paper_id',
    'idx_notes_created_at',
    'idx_notes_updated_at',
    'idx_collections_user_id',
    'idx_collections_name',
    'idx_collections_created_at',
    'idx_collections_updated_at',
    'idx_reviews_next_due',
    'idx_reviews_ease',
    'idx_reviews_created_at',
    'idx_reviews_updated_at'
  ]
}

/**
 * Validate the complete database schema
 */
export async function validateDatabaseSchema(): Promise<SchemaValidationResult> {
  const result: SchemaValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    details: {
      tables: [],
      indexes: [],
      constraints: []
    }
  }

  try {
    // Validate tables and columns
    for (const [tableName, tableSchema] of Object.entries(EXPECTED_SCHEMA.tables)) {
      const tableResult = await validateTable(tableName, tableSchema)
      result.details.tables.push(tableResult)
      
      if (!tableResult.exists) {
        result.errors.push(`Table '${tableName}' does not exist`)
        result.isValid = false
      } else {
        // Check for missing columns
        if (tableResult.missingColumns.length > 0) {
          result.errors.push(`Table '${tableName}' missing columns: ${tableResult.missingColumns.join(', ')}`)
          result.isValid = false
        }
        
        // Check for extra columns (warnings only)
        if (tableResult.extraColumns.length > 0) {
          result.warnings.push(`Table '${tableName}' has extra columns: ${tableResult.extraColumns.join(', ')}`)
        }
        
        // Check column types and properties
        for (const column of tableResult.columns) {
          if (column.exists && column.dataType !== column.expectedDataType) {
            result.warnings.push(`Table '${tableName}' column '${column.columnName}' has type '${column.dataType}' but expected '${column.expectedDataType}'`)
          }
        }
      }
    }

    // Validate indexes
    for (const indexName of EXPECTED_SCHEMA.indexes) {
      const indexResult = await validateIndex(indexName)
      result.details.indexes.push(indexResult)
      
      if (!indexResult.exists) {
        result.warnings.push(`Index '${indexName}' does not exist`)
      }
    }

    console.log(`✅ Schema validation completed. Valid: ${result.isValid}, Errors: ${result.errors.length}, Warnings: ${result.warnings.length}`)
    
  } catch (error) {
    result.errors.push(`Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    result.isValid = false
  }

  return result
}

/**
 * Validate a specific table and its columns
 */
async function validateTable(tableName: string, expectedSchema: any): Promise<TableValidationResult> {
  const result: TableValidationResult = {
    tableName,
    exists: false,
    columns: [],
    missingColumns: [],
    extraColumns: []
  }

  try {
    // Check if table exists
    const tableExistsResult = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = $1
      );
    `, [tableName])

    result.exists = tableExistsResult.rows[0].exists

    if (!result.exists) {
      return result
    }

    // Get actual columns
    const columnsResult = await query(`
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position;
    `, [tableName])

    const actualColumns = new Set(columnsResult.rows.map((row: any) => row.column_name))
    const expectedColumns = new Set(Object.keys(expectedSchema.columns))

    // Find missing and extra columns
    result.missingColumns = Array.from(expectedColumns).filter(col => !actualColumns.has(col))
    result.extraColumns = Array.from(actualColumns).filter(col => !expectedColumns.has(col))

    // Validate each expected column
    for (const [columnName, expectedColumn] of Object.entries(expectedSchema.columns) as any) {
      const actualColumn = columnsResult.rows.find((row: any) => row.column_name === columnName)

      const columnResult: ColumnValidationResult = {
        columnName,
        exists: !!actualColumn,
        dataType: actualColumn?.data_type || '',
        expectedDataType: expectedColumn.type,
        isNullable: actualColumn?.is_nullable === 'YES',
        expectedNullable: expectedColumn.nullable,
        hasDefault: !!actualColumn?.column_default,
        expectedDefault: expectedColumn.hasDefault
      }

      result.columns.push(columnResult)
    }

  } catch (error) {
    console.error(`Error validating table ${tableName}:`, error)
  }

  return result
}

/**
 * Validate a specific index
 */
async function validateIndex(indexName: string): Promise<IndexValidationResult> {
  const result: IndexValidationResult = {
    indexName,
    exists: false,
    tableName: ''
  }

  try {
    const indexResult = await query(`
      SELECT
        schemaname,
        tablename,
        indexname
      FROM pg_indexes
      WHERE indexname = $1;
    `, [indexName])

    result.exists = indexResult.rows.length > 0
    if (result.exists) {
      result.tableName = indexResult.rows[0].tablename
    }

  } catch (error) {
    console.error(`Error validating index ${indexName}:`, error)
  }

  return result
}

/**
 * Quick schema validation for startup checks
 */
export async function quickSchemaValidation(): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = []

  try {
    // Check critical tables exist
    const criticalTables = ['users', 'papers', 'notes', 'collections', 'reviews']

    for (const tableName of criticalTables) {
      const result = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = $1
        );
      `, [tableName])

      if (!result.rows[0].exists) {
        errors.push(`Critical table '${tableName}' does not exist`)
      }
    }

    // Check critical columns exist
    const criticalColumns = [
      { table: 'papers', column: 'user_id' },
      { table: 'reviews', column: 'created_at' },
      { table: 'papers', column: 'zotero_group_id' }
    ]

    for (const { table, column } of criticalColumns) {
      const result = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_name = $1 AND column_name = $2
        );
      `, [table, column])

      if (!result.rows[0].exists) {
        errors.push(`Critical column '${column}' does not exist in table '${table}'`)
      }
    }

  } catch (error) {
    errors.push(`Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
