import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'

const bulkSkipSchema = z.object({
  paperIds: z.array(z.string()).min(1, 'At least one paper ID is required')
})

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    // Parse request body
    let requestBody
    try {
      const bodyText = await request.text()
      if (bodyText) {
        requestBody = bulkSkipSchema.parse(JSON.parse(bodyText))
      } else {
        return createErrorResponse(
          'Request body required',
          400,
          correlationId,
          ['paperIds array is required'],
          'Request body must contain paperIds array'
        )
      }
    } catch (parseError) {
      return createErrorResponse(
        'Invalid request body',
        400,
        correlationId,
        ['Invalid JSON or missing required fields'],
        'Request body must be valid JSON with paperIds array'
      )
    }

    logger.info('Bulk skipping Zotero sync for papers', { 
      userId, 
      paperCount: requestBody.paperIds.length 
    })

    // New sync flow is now the default

    try {
      // Verify all papers belong to the user and update their sync status
      let updatedCount = 0
      const errors: string[] = []

      for (const paperId of requestBody.paperIds) {
        try {
          // Get the paper to verify ownership
          const paper = await papers.getById(paperId)
          if (!paper) {
            errors.push(`Paper ${paperId} not found`)
            continue
          }

          // Check if user can access this paper
          if (!(await canAccessResource(user, paper.userId))) {
            errors.push(`Access denied for paper ${paperId}`)
            continue
          }

          // Update sync status to 'skipped'
          await papers.update(paperId, {
            zoteroSyncStatus: 'skipped',
            updatedAt: new Date().toISOString()
          })

          updatedCount++
        } catch (error) {
          errors.push(`Failed to update paper ${paperId}: ${error.message}`)
        }
      }

      logger.info('Bulk skip completed', { 
        userId, 
        requested: requestBody.paperIds.length,
        updated: updatedCount,
        errors: errors.length
      })

      if (errors.length > 0 && updatedCount === 0) {
        // All failed
        return createErrorResponse(
          'Bulk skip failed',
          400,
          correlationId,
          errors,
          'Failed to skip any papers'
        )
      }

      const responseData = {
        updated: updatedCount,
        requested: requestBody.paperIds.length,
        errors: errors.length > 0 ? errors : undefined
      }

      if (errors.length > 0) {
        // Partial success
        return createSuccessResponse(responseData, correlationId)
      } else {
        // Complete success
        return createSuccessResponse(responseData, correlationId)
      }

    } catch (error) {
      logger.error('Error during bulk skip', { 
        error: error.message, 
        userId 
      })

      return createErrorResponse(
        'Bulk skip failed',
        500,
        correlationId,
        ['An unexpected error occurred during bulk skip'],
        'Internal error'
      )
    }
  } catch (error: any) {
    logger.error('Error in bulk-skip-sync endpoint', {
      error: error.message,
      stack: error.stack,
      correlationId
    })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred'
    )
  }
})
