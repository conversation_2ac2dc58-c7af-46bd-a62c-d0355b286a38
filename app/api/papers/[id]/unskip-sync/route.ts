import { NextRequest, NextResponse } from 'next/server'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params

    logger.info('Re-enabling Zotero sync for paper', { userId, paperId: id })

    // Get the paper
    const paper = await papers.getById(id)
    if (!paper) {
      return createErrorResponse(
        'Paper not found',
        404,
        correlationId,
        undefined,
        'Paper does not exist'
      )
    }

    // Check if user can access this paper
    if (!(await canAccessResource(user, paper.userId))) {
      return createErrorResponse(
        'Access denied',
        403,
        correlationId,
        undefined,
        'You do not have permission to access this paper'
      )
    }

    try {
      // Update sync status back to 'not_synced' to re-enable sync
      await papers.update(id, {
        zoteroSyncStatus: 'not_synced',
        updatedAt: new Date().toISOString()
      })

      logger.info('Paper Zotero sync re-enabled successfully', { 
        userId, 
        paperId: id 
      })

      return createSuccessResponse({
        message: 'Zotero sync re-enabled for this paper',
        paperId: id,
        syncStatus: 'not_synced'
      }, correlationId)

    } catch (error) {
      logger.error('Error re-enabling Zotero sync', { error: error.message, userId, paperId: id })
      return createErrorResponse(
        'Unskip failed',
        500,
        correlationId,
        ['An unexpected error occurred while re-enabling sync'],
        'Internal unskip error'
      )
    }
  } catch (error: any) {
    logger.error('Error in unskip-sync endpoint', {
      error: error.message,
      stack: error.stack,
      correlationId
    })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred'
    )
  }
})
