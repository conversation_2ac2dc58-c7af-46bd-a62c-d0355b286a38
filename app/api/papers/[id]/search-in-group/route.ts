import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { papers, notes } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'
import { getZoteroSettingsFromPreferences, validateZoteroSettings } from '@/lib/zotero-sync-service'
import { ZoteroClient } from '@/lib/zotero-client'
import { getCachedZoteroSearch, CacheKeys } from '@/lib/cache'

const searchInGroupSchema = z.object({
  libraryType: z.enum(['user', 'group']),
  libraryId: z.string().optional()
})

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params

    // Parse request body
    let requestBody
    try {
      const bodyText = await request.text()
      if (bodyText) {
        requestBody = searchInGroupSchema.parse(JSON.parse(bodyText))
      } else {
        return createErrorResponse(
          'Request body required',
          400,
          correlationId,
          ['libraryType is required'],
          'Request body must contain libraryType and optional libraryId'
        )
      }
    } catch (parseError) {
      return createErrorResponse(
        'Invalid request body',
        400,
        correlationId,
        ['Invalid JSON or missing required fields'],
        'Request body must be valid JSON with libraryType and optional libraryId'
      )
    }

    logger.info('Searching for paper in Zotero group', { 
      userId, 
      paperId: id, 
      libraryType: requestBody.libraryType,
      libraryId: requestBody.libraryId
    })

    // New sync flow is now the default

    // Validate group library has libraryId
    if (requestBody.libraryType === 'group' && !requestBody.libraryId) {
      return createErrorResponse(
        'Invalid request',
        400,
        correlationId,
        ['libraryId is required for group libraries'],
        'Group libraries must specify a libraryId'
      )
    }

    // Get the paper
    const paper = await papers.getById(id)
    if (!paper) {
      return createErrorResponse(
        'Paper not found',
        404,
        correlationId,
        undefined,
        'Paper does not exist'
      )
    }

    // Check if user can access this paper
    if (!(await canAccessResource(user, paper.userId))) {
      return createErrorResponse(
        'Access denied',
        403,
        correlationId,
        undefined,
        'You do not have permission to access this paper'
      )
    }

    // Get Zotero settings
    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})
    
    if (!zoteroSettings.enabled) {
      return createErrorResponse(
        'Zotero sync is disabled',
        400,
        correlationId,
        [
          'Zotero sync is currently disabled in your settings.',
          'To enable sync: Go to Settings → Zotero Integration → Enable Zotero Sync',
          'Make sure you have configured your API key and library settings.'
        ],
        'Please enable Zotero sync in your settings to use this feature'
      )
    }

    // Validate Zotero settings
    const validation = validateZoteroSettings(zoteroSettings)
    if (!validation.valid) {
      return createErrorResponse(
        'Invalid Zotero configuration',
        400,
        correlationId,
        validation.errors,
        'Configuration validation failed'
      )
    }

    try {
      // Create Zotero client and search for existing item
      const client = new ZoteroClient(zoteroSettings.apiKey!)
      
      let existingItem = null
      let searchMethod = 'none'
      
      // Try to find existing item by DOI first, then by title (with caching)
      if (paper.doi) {
        const doiCacheKey = CacheKeys.zoteroSearchByDoi(userId, paper.doi, requestBody.libraryType, requestBody.libraryId || '')
        existingItem = await getCachedZoteroSearch(
          userId,
          doiCacheKey,
          async () => await client.findItemByDOI(
            requestBody.libraryType,
            requestBody.libraryId || '',
            paper.doi!
          )
        )
        searchMethod = 'doi'
      }

      if (!existingItem && paper.title) {
        const titleCacheKey = CacheKeys.zoteroSearchByTitle(userId, paper.title, paper.year, requestBody.libraryType, requestBody.libraryId || '')
        existingItem = await getCachedZoteroSearch(
          userId,
          titleCacheKey,
          async () => await client.findItemByTitle(
            requestBody.libraryType,
            requestBody.libraryId || '',
            paper.title!,
            paper.year
          )
        )
        searchMethod = 'title'
      }

      if (!existingItem) {
        logger.info('Paper not found in specified Zotero group', { 
          userId, 
          paperId: id, 
          libraryType: requestBody.libraryType,
          libraryId: requestBody.libraryId,
          searchMethod
        })
        
        return createErrorResponse(
          'Paper not found',
          404,
          correlationId,
          ['Paper not found in the specified Zotero group'],
          'Paper not found in group'
        )
      }

      // Found existing item - create mapping and sync notes
      logger.info('Found existing item in Zotero group', {
        userId,
        paperId: id,
        itemKey: existingItem.key,
        libraryType: requestBody.libraryType,
        libraryId: requestBody.libraryId,
        searchMethod
      })

      // Get note for syncing
      const note = await notes.getByPaperId(id)
      if (!note) {
        return createErrorResponse(
          'Note not found',
          400,
          correlationId,
          ['Note not found for paper'],
          'Cannot sync without note data'
        )
      }

      // Create or update note
      const noteResult = await client.createOrUpdateNote(
        requestBody.libraryType,
        requestBody.libraryId || '',
        existingItem.key,
        note,
        paper.zoteroNoteKey
      )

      if (!noteResult.success) {
        return createErrorResponse(
          'Note sync failed',
          400,
          correlationId,
          [noteResult.error || 'Failed to sync note'],
          'Failed to sync note to existing item'
        )
      }

      // Store mapping in database
      await papers.update(id, {
        zoteroItemKey: existingItem.key,
        zoteroNoteKey: noteResult.noteKey!,
        zoteroGroupId: requestBody.libraryType === 'group' ? requestBody.libraryId : null,
        zoteroLastSynced: new Date().toISOString(),
        zoteroSyncStatus: 'mapped'
      })

      const responseData = {
        message: `Found and synced to existing item in ${requestBody.libraryType === 'user' ? 'personal library' : 'group'}`,
        itemKey: existingItem.key,
        noteKey: noteResult.noteKey!,
        syncedAt: new Date().toISOString(),
        searchMethod
      }
      
      return createSuccessResponse(responseData, correlationId)

    } catch (error) {
      logger.error('Error during Zotero search', { error: error.message, userId, paperId: id })
      return createErrorResponse(
        'Search failed',
        500,
        correlationId,
        ['An unexpected error occurred during search'],
        'Internal search error'
      )
    }
  } catch (error: any) {
    logger.error('Error in search-in-group endpoint', {
      error: error.message,
      stack: error.stack,
      correlationId
    })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred'
    )
  }
})
