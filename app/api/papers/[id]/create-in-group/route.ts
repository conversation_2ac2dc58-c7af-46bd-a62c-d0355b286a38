import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'
import { getZoteroSettingsFromPreferences, validateZoteroSettings } from '@/lib/zotero-sync-service'
import { ZoteroSyncServiceV2 } from '@/lib/zotero-sync-service-v2'

const createInGroupSchema = z.object({
  libraryType: z.enum(['user', 'group']),
  libraryId: z.string().optional()
})

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params

    // Parse request body
    let requestBody
    try {
      const bodyText = await request.text()
      if (bodyText) {
        requestBody = createInGroupSchema.parse(JSON.parse(bodyText))
      } else {
        return createErrorResponse(
          'Request body required',
          400,
          correlationId,
          ['libraryType is required'],
          'Request body must contain libraryType and optional libraryId'
        )
      }
    } catch (parseError) {
      return createErrorResponse(
        'Invalid request body',
        400,
        correlationId,
        ['Invalid JSON or missing required fields'],
        'Request body must be valid JSON with libraryType and optional libraryId'
      )
    }

    logger.info('Creating paper in selected Zotero group', { 
      userId, 
      paperId: id, 
      libraryType: requestBody.libraryType,
      libraryId: requestBody.libraryId
    })

    // New sync flow is now the default

    // Validate group library has libraryId
    if (requestBody.libraryType === 'group' && !requestBody.libraryId) {
      return createErrorResponse(
        'Invalid request',
        400,
        correlationId,
        ['libraryId is required for group libraries'],
        'Group libraries must specify a libraryId'
      )
    }

    // Get the paper
    const paper = await papers.getById(id)
    if (!paper) {
      return createErrorResponse(
        'Paper not found',
        404,
        correlationId,
        undefined,
        'Paper does not exist'
      )
    }

    // Check if user can access this paper
    if (!(await canAccessResource(user, paper.userId))) {
      return createErrorResponse(
        'Access denied',
        403,
        correlationId,
        undefined,
        'You do not have permission to access this paper'
      )
    }

    // Get Zotero settings
    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})
    
    if (!zoteroSettings.enabled) {
      return createErrorResponse(
        'Zotero sync is disabled',
        400,
        correlationId,
        [
          'Zotero sync is currently disabled in your settings.',
          'To enable sync: Go to Settings → Zotero Integration → Enable Zotero Sync',
          'Make sure you have configured your API key and library settings.'
        ],
        'Please enable Zotero sync in your settings to use this feature'
      )
    }

    // Validate Zotero settings
    const validation = validateZoteroSettings(zoteroSettings)
    if (!validation.valid) {
      return createErrorResponse(
        'Invalid Zotero configuration',
        400,
        correlationId,
        validation.errors,
        'Configuration validation failed'
      )
    }

    try {
      // Create sync service and create in selected group
      const syncServiceV2 = new ZoteroSyncServiceV2(zoteroSettings.apiKey!, zoteroSettings, userId)
      
      const result = await syncServiceV2.createInGroup(
        id, 
        requestBody.libraryType, 
        requestBody.libraryId
      )

      if (result.success) {
        logger.info('Paper created in selected Zotero group successfully', { 
          userId, 
          paperId: id, 
          libraryType: requestBody.libraryType,
          libraryId: requestBody.libraryId,
          itemKey: result.itemKey,
          noteKey: result.noteKey,
          telemetry: result.telemetry
        })
        
        const responseData: any = {
          message: result.message,
          itemKey: result.itemKey,
          noteKey: result.noteKey,
          syncedAt: new Date().toISOString()
        }

        // Include telemetry if available
        if (result.telemetry) {
          responseData.telemetry = result.telemetry
        }
        
        return createSuccessResponse(responseData, correlationId)
      } else {
        logger.error('Failed to create paper in selected Zotero group', { 
          userId, 
          paperId: id, 
          libraryType: requestBody.libraryType,
          libraryId: requestBody.libraryId,
          error: result.error,
          telemetry: result.telemetry
        })
        
        return createErrorResponse(
          'Creation failed',
          400,
          correlationId,
          [result.error || 'Unknown error occurred'],
          'Failed to create paper in selected Zotero group'
        )
      }
    } catch (error) {
      logger.error('Error during Zotero creation', { error: error.message, userId, paperId: id })
      return createErrorResponse(
        'Creation failed',
        500,
        correlationId,
        ['An unexpected error occurred during creation'],
        'Internal creation error'
      )
    }
  } catch (error: any) {
    logger.error('Error in create-in-group endpoint', {
      error: error.message,
      stack: error.stack,
      correlationId
    })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred'
    )
  }
})
