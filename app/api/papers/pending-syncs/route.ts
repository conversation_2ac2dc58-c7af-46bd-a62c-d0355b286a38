import { NextRequest, NextResponse } from 'next/server'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Fetching papers pending Zotero sync', { userId })

    // New sync flow is now the default

    try {
      // Get papers that don't have Zotero mappings or are in pending/error state
      const pendingPapers = await papers.getPendingSyncs(userId)

      logger.info('Successfully fetched pending syncs', { 
        userId, 
        count: pendingPapers.length 
      })

      return createSuccessResponse(pendingPapers, correlationId)

    } catch (error) {
      logger.error('Error fetching pending syncs', { 
        error: error.message, 
        userId 
      })

      return createErrorResponse(
        'Failed to fetch pending syncs',
        500,
        correlationId,
        ['An unexpected error occurred while fetching pending syncs'],
        'Internal error'
      )
    }
  } catch (error: any) {
    logger.error('Error in pending-syncs endpoint', {
      error: error.message,
      stack: error.stack,
      correlationId
    })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred'
    )
  }
})
