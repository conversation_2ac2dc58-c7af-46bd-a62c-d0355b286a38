"use client"

import { useState, useEffect, use<PERSON>allback, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Star, Trash2, BookOpen, BookOpenCheck, ExternalLink, FileText, BarChart3, RefreshCw, ChevronDown, ChevronUp, Loader2, CheckCircle, AlertCircle, Link as LinkIcon, MoreVertical, XCircle, RotateCcw } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ConditionalSidebarTrigger } from "@/components/ui/conditional-sidebar-trigger"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { CollectionToggle } from "@/components/collection-toggle"
import { ZoteroSyncDialog } from "@/components/zotero-sync-dialog"
import { ZoteroUserSelectionDialog } from "@/components/zotero-user-selection-dialog"
import { authenticatedFetch, useErrorLogger } from "@/lib/utils"
import type { Paper, Note } from "@/lib/types"

export default function PaperPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { logError } = useErrorLogger('PaperPage')
  const [paper, setPaper] = useState<Paper | null>(null)
  const [note, setNote] = useState<Note | null>(null)
  const [loading, setLoading] = useState(true)
  const [reviewStatus, setReviewStatus] = useState<{
    inReview: boolean
    isDue: boolean
    daysUntilDue: number | null
  } | null>(null)
  const [isEnriching, setIsEnriching] = useState(false)
  const [isAbstractOpen, setIsAbstractOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [isSyncingZotero, setIsSyncingZotero] = useState(false)
  const [zoteroSyncStatus, setZoteroSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle')
  const [lastSyncError, setLastSyncError] = useState<{ message: string; retryable?: boolean; action?: string } | null>(null)
  const [showZoteroSyncDialog, setShowZoteroSyncDialog] = useState(false)
  const [showZoteroUserSelectionDialog, setShowZoteroUserSelectionDialog] = useState(false)
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const debouncedSaveRef = useRef<NodeJS.Timeout | null>(null)

  const paperId = params.id as string

  // Get collection context from URL search params if available
  const [collectionId, setCollectionId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const fromCollection = urlParams.get('collection')
      setCollectionId(fromCollection)
    }
  }, [])

  useEffect(() => {
    fetchPaper()
    fetchNote()
    fetchReviewStatus()
  }, [paperId])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      if (debouncedSaveRef.current) {
        clearTimeout(debouncedSaveRef.current)
      }
    }
  }, [])

  const fetchPaper = async () => {
    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}`, {}, {
        component: 'PaperPage',
        action: 'fetch_paper'
      })
      if (response.ok) {
        const responseData = await response.json()
        // Handle wrapped response format
        const paperData = responseData.data || responseData
        setPaper(paperData)

        // Automatically remove from review queue when paper is viewed
        await autoRemoveFromReview()
      } else if (response.status === 404) {
        toast({
          title: "Paper not found",
          description: "The paper you're looking for doesn't exist",
          variant: "destructive"
        })
        router.push("/papers")
      } else if (response.status === 403) {
        toast({
          title: "Access denied",
          description: "You don't have permission to view this paper",
          variant: "destructive"
        })
        router.push("/papers")
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      logError("fetch_paper", error, { paperId })
      toast({
        title: "Failed to load paper",
        description: "Please try again later",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchNote = async () => {
    try {
      const response = await authenticatedFetch(`/api/notes/${paperId}`, {}, {
        component: 'PaperPage',
        action: 'fetch_note'
      })
      if (response.ok) {
        const data = await response.json()
        setNote(data)
      } else {
        // Create empty note if none exists
        setNote({
          id: "",
          paperId,
          quickSummary: "",
          keyIdeas: ["", "", ""],
        })
      }
    } catch (error) {
      logError("fetch_note", error, { paperId })
    }
  }

  const fetchReviewStatus = async () => {
    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/review`, {}, {
        component: 'PaperPage',
        action: 'fetch_review_status'
      })
      if (response.ok) {
        const data = await response.json()
        setReviewStatus(data)
      }
    } catch (error) {
      logError("fetch_review_status", error, { paperId })
    }
  }

  const addToReview = async () => {
    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/review`, {
        method: "POST",
      }, {
        component: 'PaperPage',
        action: 'add_to_review'
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Added to review queue",
          description: data.wasExisting ? "Paper review updated" : "Paper added to review queue"
        })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to add to review")
      }
    } catch (error) {
      logError("add_to_review", error, { paperId })
      toast({ title: "Failed to add to review", variant: "destructive" })
    }
  }

  const removeFromReview = async () => {
    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/review`, {
        method: "DELETE",
      }, {
        component: 'PaperPage',
        action: 'remove_from_review'
      })

      if (response.ok) {
        toast({ title: "Removed from review queue" })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to remove from review")
      }
    } catch (error) {
      logError("remove_from_review", error, { paperId })
      toast({ title: "Failed to remove from review", variant: "destructive" })
    }
  }

  const autoRemoveFromReview = async () => {
    try {
      // First check if the paper is in the review queue
      const reviewResponse = await authenticatedFetch(`/api/papers/${paperId}/review`, {}, {
        component: 'PaperPage',
        action: 'check_review_status'
      })

      if (reviewResponse.ok) {
        const reviewData = await reviewResponse.json()
        // If paper is in review queue, remove it silently
        if (reviewData && reviewData.inReview) {
          const deleteResponse = await authenticatedFetch(`/api/papers/${paperId}/review`, {
            method: "DELETE",
          }, {
            component: 'PaperPage',
            action: 'auto_remove_from_review'
          })

          if (deleteResponse.ok) {
            // Silently removed - no toast notification for automatic removal
            fetchReviewStatus() // Refresh status
          }
        }
      }
    } catch (error) {
      // Silently fail for automatic removal - don't show error to user
      logError("auto_remove_from_review", error, { paperId })
    }
  }

  const enrichPaper = async () => {
    if (!paper) return

    setIsEnriching(true)
    try {
      const enrichRequest: any = {}

      if (paper.title && paper.title.trim()) {
        enrichRequest.title = paper.title.trim()
      }

      if (paper.authors && paper.authors.length > 0) {
        enrichRequest.authors = paper.authors
      }

      if (paper.doi && paper.doi.trim()) {
        enrichRequest.doi = paper.doi.trim()
      }

      // Include collection context if available
      if (collectionId) {
        enrichRequest.collectionId = collectionId
      }

      const response = await authenticatedFetch("/api/papers/enrich", {
        method: "POST",
        body: JSON.stringify(enrichRequest)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Update paper with enriched metadata
          const enrichedPaper = { ...paper, ...data.metadata }
          setPaper(enrichedPaper)

          // Save only the enriched metadata fields to avoid validation issues
          await savePaper(data.metadata)

          toast({
            title: "Paper enriched successfully",
            description: `Added ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
          })
        } else {
          toast({
            title: "No additional data found",
            description: data.message || "Paper not found in Semantic Scholar database"
          })
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      logError("enrich_paper", error, { paperId, paperTitle: paper?.title })
      toast({ title: "Failed to enrich paper", variant: "destructive" })
    } finally {
      setIsEnriching(false)
    }
  }

  const syncToZotero = async () => {
    if (!paper) return

    setIsSyncingZotero(true)
    setZoteroSyncStatus('syncing')

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/sync-zotero`, {
        method: "POST"
      })

      const data = await response.json()

      if (response.ok) {
        setZoteroSyncStatus('success')
        toast({
          title: "Synced to Zotero",
          description: data.data?.message || "Paper synced successfully to Zotero",
          duration: 6000 // Show longer so user can read the destination
        })

        // Update paper with sync information
        setPaper(prev => prev ? {
          ...prev,
          zoteroSyncStatus: 'synced',
          zoteroLastSynced: new Date().toISOString()
        } : null)

        // Reset status after 3 seconds
        setTimeout(() => setZoteroSyncStatus('idle'), 3000)
      } else {
        // Check if this is the new "requires user selection" case (409 status)
        if (response.status === 409 && data.details?.requiresUserSelection) {
          setZoteroSyncStatus('idle') // Reset status since we're showing a dialog
          setShowZoteroUserSelectionDialog(true)
          return // Don't show error toast, let user make selection
        }

        setZoteroSyncStatus('error')
        const errorMessage = data.errors?.[0] || data.error || "Failed to sync to Zotero"
        const isRetryable = data.details?.retryable || false
        const suggestedAction = data.details?.action

        // Store error details for retry functionality
        setLastSyncError({
          message: errorMessage,
          retryable: isRetryable,
          action: suggestedAction
        })

        // Check if this is a case where legacy user selection is required
        if (errorMessage.includes('Failed to sync to any available Zotero destination') ||
            errorMessage.includes('403') ||
            errorMessage.includes('Cannot edit item')) {
          // Show the legacy destination selection dialog
          setShowZoteroSyncDialog(true)
        } else {
          toast({
            title: response.status === 503 ? "Sync temporarily failed" : "Sync failed",
            description: errorMessage,
            variant: "destructive",
            duration: isRetryable ? 8000 : 5000 // Show longer for retryable errors
          })
        }

        // Reset status after 5 seconds
        setTimeout(() => setZoteroSyncStatus('idle'), 5000)
      }
    } catch (error) {
      logError("sync_to_zotero", error, { paperId, paperTitle: paper?.title })
      setZoteroSyncStatus('error')
      toast({
        title: "Sync failed",
        description: "Network error occurred while syncing to Zotero",
        variant: "destructive"
      })

      // Reset status after 5 seconds
      setTimeout(() => setZoteroSyncStatus('idle'), 5000)
    } finally {
      setIsSyncingZotero(false)
    }
  }

  const skipZoteroSync = async () => {
    if (!paper) return

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/skip-sync`, {
        method: "POST"
      })

      if (response.ok) {
        toast({
          title: "Zotero sync skipped",
          description: "This paper will not be synced to Zotero",
          duration: 3000
        })

        // Update paper status
        setPaper(prev => prev ? {
          ...prev,
          zoteroSyncStatus: 'skipped'
        } : null)
      } else {
        toast({
          title: "Failed to skip sync",
          description: "Could not update sync status",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Network error",
        description: "Failed to skip Zotero sync",
        variant: "destructive"
      })
    }
  }

  const unskipZoteroSync = async () => {
    if (!paper) return

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/unskip-sync`, {
        method: "POST"
      })

      if (response.ok) {
        toast({
          title: "Zotero sync re-enabled",
          description: "This paper can now be synced to Zotero",
          duration: 3000
        })

        // Update paper status
        setPaper(prev => prev ? {
          ...prev,
          zoteroSyncStatus: 'not_synced'
        } : null)
      } else {
        toast({
          title: "Failed to re-enable sync",
          description: "Could not update sync status",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Network error",
        description: "Failed to re-enable Zotero sync",
        variant: "destructive"
      })
    }
  }

  const savePaper = useCallback(
    async (updatedPaper: Partial<Paper>) => {
      if (!paper) return

      // Clear any existing timeout
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }

      // Set saving status
      setSaveStatus('saving')
      setIsSaving(true)

      try {
        const response = await authenticatedFetch(`/api/papers/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedPaper),
        }, {
          component: 'PaperPage',
          action: 'save_paper'
        })

        if (response.ok) {
          const responseData = await response.json()
          // Handle both wrapped and unwrapped responses
          const updated = responseData.data || responseData

          // Only update the specific fields that were changed, not the entire paper
          setPaper(prev => prev ? { ...prev, ...updated } : updated)

          setSaveStatus('saved')

          // Reset status after 2 seconds
          saveTimeoutRef.current = setTimeout(() => {
            setSaveStatus('idle')
          }, 2000)
        } else {
          throw new Error('Failed to save paper')
        }
      } catch (error) {
        logError("save_paper", error, { paperId, paperTitle: paper?.title })
        setSaveStatus('error')
        toast({
          title: "Failed to save changes",
          description: "Please try again",
          variant: "destructive"
        })

        // Reset error status after 3 seconds
        saveTimeoutRef.current = setTimeout(() => {
          setSaveStatus('idle')
        }, 3000)
      } finally {
        setIsSaving(false)
      }
    },
    [paper, paperId, toast],
  )

  // Debounced save function to prevent saving on every keystroke
  const debouncedSavePaper = useCallback(
    (updatedPaper: Partial<Paper>) => {
      // Clear existing debounce timeout
      if (debouncedSaveRef.current) {
        clearTimeout(debouncedSaveRef.current)
      }

      // Set new timeout
      debouncedSaveRef.current = setTimeout(() => {
        savePaper(updatedPaper)
      }, 1000) // Wait 1 second after user stops typing
    },
    [savePaper]
  )

  const saveNote = useCallback(
    async (updatedNote: Partial<Note>) => {
      if (!note) return

      try {
        const response = await authenticatedFetch(`/api/notes/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedNote),
        }, {
          component: 'PaperPage',
          action: 'save_note'
        })

        if (response.ok) {
          const updated = await response.json()
          setNote(updated)
        }
      } catch (error) {
        logError("save_note", error, { paperId, noteId: note?.id })
      }
    },
    [note, paperId],
  )

  const toggleStar = async () => {
    if (!paper) return

    try {
      await authenticatedFetch(`/api/papers/${paperId}/star`, { method: "POST" }, {
        component: 'PaperPage',
        action: 'toggle_star'
      })
      setPaper((prev) => (prev ? { ...prev, starred: !prev.starred } : null))
    } catch (error) {
      logError("toggle_star", error, { paperId, paperTitle: paper?.title })
    }
  }

  const deletePaper = async () => {
    if (!paper) return

    if (!confirm(`Are you sure you want to delete "${paper.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}`, { method: "DELETE" }, {
        component: 'PaperPage',
        action: 'delete_paper'
      })
      if (response.ok) {
        toast({ title: "Paper deleted successfully" })
        router.push("/papers")
      } else {
        throw new Error("Failed to delete paper")
      }
    } catch (error) {
      logError("delete_paper", error, { paperId, paperTitle: paper?.title })
      toast({ title: "Failed to delete paper", variant: "destructive" })
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs or textareas
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle Ctrl/Cmd + S for save (though auto-save is already implemented)
      if ((e.metaKey || e.ctrlKey) && e.key === "s") {
        e.preventDefault()
        // Auto-save is handled by debounced updates
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "s":
          e.preventDefault()
          toggleStar()
          break
        case "e":
          e.preventDefault()
          // Focus on the title field to start editing
          const titleField = document.getElementById("title")
          if (titleField) {
            titleField.focus()
          }
          break
        case "escape":
          e.preventDefault()
          router.push("/papers")
          break
        case "delete":
        case "backspace":
          if (e.shiftKey) {
            e.preventDefault()
            deletePaper()
          }
          break
        case "r":
          e.preventDefault()
          if (reviewStatus?.inReview) {
            removeFromReview()
          } else {
            addToReview()
          }
          break
      }

      // Number keys 1-3 for key ideas
      if (e.key >= "1" && e.key <= "3") {
        const index = Number.parseInt(e.key) - 1
        const ideaInput = document.getElementById(`idea-${index}`)
        if (ideaInput) {
          e.preventDefault()
          ideaInput.focus()
        }
      }

      // 'q' key for quick summary
      if (e.key.toLowerCase() === "q") {
        const summaryInput = document.getElementById("quickSummary")
        if (summaryInput) {
          e.preventDefault()
          summaryInput.focus()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [router])

  if (loading) {
    return <div className="p-4">Loading...</div>
  }

  if (!paper) {
    return <div className="p-4">Paper not found</div>
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <ConditionalSidebarTrigger />
          <Link href="/papers">
            <Button variant="ghost" size="sm" title="Back to papers list">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-xl font-semibold truncate flex-1">{paper.title}</h1>
          <CollectionToggle paperId={paperId} />
          <Button variant="ghost" size="sm" onClick={toggleStar} title={paper.starred ? "Remove star" : "Star this paper"}>
            <Star className={`h-4 w-4 ${paper.starred ? "fill-yellow-400 text-yellow-400" : ""}`} />
          </Button>
          {reviewStatus?.inReview ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={removeFromReview}
              className="text-green-600 hover:text-green-700"
              title="Remove from review queue"
            >
              <BookOpenCheck className="h-4 w-4" />
            </Button>
          ) : (
            <Button variant="ghost" size="sm" onClick={addToReview} title="Add to review queue">
              <BookOpen className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={enrichPaper}
            disabled={isEnriching}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
            {isEnriching ? 'Enriching...' : 'Enrich'}
          </Button>
          {/* Zotero Sync Controls */}
          {paper?.zoteroSyncStatus === 'skipped' ? (
            <Button
              variant="outline"
              size="sm"
              onClick={unskipZoteroSync}
              className="text-gray-600 border-gray-200 hover:bg-gray-50"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Unskip & Sync
            </Button>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={isSyncingZotero}
                  className={`${
                    zoteroSyncStatus === 'success'
                      ? 'text-green-600 border-green-200 hover:bg-green-50'
                      : zoteroSyncStatus === 'error'
                      ? 'text-red-600 border-red-200 hover:bg-red-50'
                      : 'text-purple-600 border-purple-200 hover:bg-purple-50'
                  }`}
                >
                  {zoteroSyncStatus === 'syncing' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : zoteroSyncStatus === 'success' ? (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  ) : zoteroSyncStatus === 'error' ? (
                    <AlertCircle className="h-4 w-4 mr-2" />
                  ) : (
                    <LinkIcon className="h-4 w-4 mr-2" />
                  )}
                  {zoteroSyncStatus === 'syncing'
                    ? 'Syncing...'
                    : zoteroSyncStatus === 'success'
                    ? 'Synced'
                    : zoteroSyncStatus === 'error'
                    ? 'Failed'
                    : 'Sync to Zotero'
                  }
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={syncToZotero} disabled={isSyncingZotero}>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  {zoteroSyncStatus === 'error' && lastSyncError?.retryable ? 'Retry Sync' : 'Sync to Zotero'}
                </DropdownMenuItem>
                {zoteroSyncStatus === 'error' && lastSyncError?.action && (
                  <>
                    <DropdownMenuSeparator />
                    <div className="px-2 py-1 text-xs text-muted-foreground">
                      {lastSyncError.action}
                    </div>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={skipZoteroSync} disabled={isSyncingZotero}>
                  <XCircle className="h-4 w-4 mr-2" />
                  Skip Zotero Sync
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <Button variant="ghost" size="sm" onClick={deletePaper}>
            <Trash2 className="h-4 w-4" />
          </Button>

          {/* Save Status Indicator */}
          {saveStatus !== 'idle' && (
            <div className="flex items-center gap-2 text-sm">
              {saveStatus === 'saving' && (
                <>
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-blue-600">Saving...</span>
                </>
              )}
              {saveStatus === 'saved' && (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-green-600">Saved</span>
                </>
              )}
              {saveStatus === 'error' && (
                <>
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-red-600">Error saving</span>
                </>
              )}
            </div>
          )}
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Paper Details */}
          <div className="space-y-6">
            {reviewStatus?.inReview && (
              <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                <BookOpenCheck className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  In Review Queue
                </span>
                {reviewStatus.isDue ? (
                  <span className="text-xs text-blue-700 dark:text-blue-300">• Due now</span>
                ) : reviewStatus.daysUntilDue !== null && (
                  <span className="text-xs text-blue-700 dark:text-blue-300">
                    • Due in {reviewStatus.daysUntilDue} day{reviewStatus.daysUntilDue !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            )}

            {/* Main Paper Information */}
            <div className="space-y-6">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium text-gray-700 dark:text-gray-300">Title</Label>
                <Textarea
                  id="title"
                  value={paper.title}
                  onChange={(e) => {
                    const newTitle = e.target.value
                    // Update UI immediately for responsive feel
                    setPaper(prev => prev ? { ...prev, title: newTitle } : null)
                    // Debounce the save to prevent excessive API calls
                    debouncedSavePaper({ title: newTitle })
                  }}
                  rows={3}
                  className="text-lg font-semibold border-gray-200 dark:border-gray-700 resize-none"
                />
              </div>

              {/* Authors */}
              <div className="space-y-2">
                <Label htmlFor="authors" className="text-sm font-medium text-gray-700 dark:text-gray-300">Authors</Label>
                <Textarea
                  id="authors"
                  value={(paper.authors || []).join(", ")}
                  onChange={(e) => {
                    const authorsText = e.target.value
                    const authors = authorsText
                      .split(",")
                      .map((a) => a.trim())
                      .filter(Boolean)
                    // Update UI immediately
                    setPaper(prev => prev ? { ...prev, authors } : null)
                    // Debounce the save
                    debouncedSavePaper({ authors })
                  }}
                  rows={2}
                  className="border-gray-200 dark:border-gray-700 resize-none"
                />
              </div>

              {/* Publication Info Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="year" className="text-sm font-medium text-gray-700 dark:text-gray-300">Year</Label>
                  <Input
                    id="year"
                    type="number"
                    value={paper.year || ""}
                    placeholder="2020"
                    onChange={(e) => {
                      const year = e.target.value ? Number.parseInt(e.target.value) : undefined
                      // Update UI immediately
                      setPaper(prev => prev ? { ...prev, year } : null)
                      // Debounce the save
                      debouncedSavePaper({ year })
                    }}
                    className="border-gray-200 dark:border-gray-700"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="venue" className="text-sm font-medium text-gray-700 dark:text-gray-300">Venue</Label>
                  <Input
                    id="venue"
                    value={paper.venue || ""}
                    onChange={(e) => {
                      const venue = e.target.value
                      // Update UI immediately
                      setPaper(prev => prev ? { ...prev, venue } : null)
                      // Debounce the save
                      debouncedSavePaper({ venue })
                    }}
                    className="border-gray-200 dark:border-gray-700"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="citationCount" className="text-sm font-medium text-gray-700 dark:text-gray-300">Citations</Label>
                  <Input
                    id="citationCount"
                    type="number"
                    value={paper.citationCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const citationCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, citationCount }
                      setPaper(updated)
                      savePaper({ citationCount })
                    }}
                    className="border-gray-200 dark:border-gray-700"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referenceCount" className="text-sm font-medium text-gray-700 dark:text-gray-300">References</Label>
                  <Input
                    id="referenceCount"
                    type="number"
                    value={paper.referenceCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const referenceCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, referenceCount }
                      setPaper(updated)
                      savePaper({ referenceCount })
                    }}
                    className="border-gray-200 dark:border-gray-700"
                  />
                </div>
              </div>

              {/* DOI */}
              <div className="space-y-2">
                <Label htmlFor="doi" className="text-sm font-medium text-gray-700 dark:text-gray-300">DOI</Label>
                <Input
                  id="doi"
                  value={paper.doi || ""}
                  placeholder="10.1000/182"
                  onChange={(e) => {
                    const doi = e.target.value
                    // Update UI immediately
                    setPaper(prev => prev ? { ...prev, doi } : null)
                    // Debounce the save
                    debouncedSavePaper({ doi })
                  }}
                  className="border-gray-200 dark:border-gray-700"
                />
              </div>

              {/* URL */}
              <div className="space-y-2">
                <Label htmlFor="url" className="text-sm font-medium text-gray-700 dark:text-gray-300">URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="url"
                    value={paper.url || ""}
                    placeholder="https://www.usenix.org/conference/usenixsecurity20/presentation/bouwman"
                    onChange={(e) => {
                      const url = e.target.value
                      // Update UI immediately
                      setPaper(prev => prev ? { ...prev, url } : null)
                      // Debounce the save
                      debouncedSavePaper({ url })
                    }}
                    className="border-gray-200 dark:border-gray-700"
                  />
                  {paper.url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(paper.url, '_blank')}
                      className="shrink-0"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label htmlFor="tags" className="text-sm font-medium text-gray-700 dark:text-gray-300">Tags</Label>
                <Input
                  id="tags"
                  value={(paper.tags || []).join(", ")}
                  placeholder="machine learning, AI, neural networks..."
                  onChange={(e) => {
                    const tagsText = e.target.value
                    const tags = tagsText
                      .split(",")
                      .map((t) => t.trim())
                      .filter(Boolean)
                    // Update UI immediately for responsive feel
                    setPaper(prev => prev ? { ...prev, tags } : null)
                    // Debounce the save to prevent excessive API calls
                    debouncedSavePaper({ tags })
                  }}
                  className="border-gray-200 dark:border-gray-700"
                />
              </div>
            </div>

            {/* Abstract Section */}
            <Collapsible open={isAbstractOpen} onOpenChange={setIsAbstractOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto hover:bg-gray-50 dark:hover:bg-gray-800">
                  <Label className="text-base font-medium text-gray-900 dark:text-gray-100">Abstract</Label>
                  {isAbstractOpen ? <ChevronUp className="h-4 w-4 text-gray-500" /> : <ChevronDown className="h-4 w-4 text-gray-500" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-3 mt-3">
                <Textarea
                  id="abstract"
                  value={paper.abstract || ""}
                  placeholder="Paper abstract..."
                  rows={6}
                  onChange={(e) => {
                    const abstract = e.target.value
                    // Update UI immediately
                    setPaper(prev => prev ? { ...prev, abstract } : null)
                    // Debounce the save
                    debouncedSavePaper({ abstract })
                  }}
                  className="border-gray-200 dark:border-gray-700 resize-none text-sm leading-relaxed"
                />
              </CollapsibleContent>
            </Collapsible>


          </div>

          {/* Notes Section */}
          {note && (
            <div className="space-y-6 border-t border-gray-200 dark:border-gray-700 pt-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Notes</h2>

              {/* Quick Summary */}
              <div className="space-y-3">
                <Label htmlFor="quickSummary" className="text-sm font-medium text-gray-700 dark:text-gray-300">Quick Summary</Label>
                <Textarea
                  id="quickSummary"
                  value={note.quickSummary || ""}
                  onChange={(e) => {
                    const updated = { ...note, quickSummary: e.target.value }
                    setNote(updated)
                    saveNote({ quickSummary: e.target.value })
                  }}
                  placeholder="The paper evaluates the actual usefulness of commercial threat intelligence (TI) feeds by comparing them against academic, open-source, and operational perspectives, showing that commercial feeds often provide limited additional value."
                  rows={3}
                  className="text-base border-gray-200 dark:border-gray-700 resize-none leading-relaxed"
                />
              </div>

              {/* Key Ideas */}
              <div className="space-y-4">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Key Ideas (use keys 1-3 to focus)</Label>
                {note.keyIdeas.map((idea, index) => (
                  <div key={index} className="space-y-3">
                    <Label htmlFor={`idea-${index}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-3">
                      <span className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold shrink-0">
                        {index + 1}
                      </span>
                      Key Idea {index + 1}
                    </Label>
                    <Textarea
                      id={`idea-${index}`}
                      value={idea}
                      onChange={(e) => {
                        const keyIdeas = [...note.keyIdeas]
                        keyIdeas[index] = e.target.value
                        const updated = { ...note, keyIdeas }
                        setNote(updated)
                        saveNote({ keyIdeas })
                      }}
                      placeholder={
                        index === 0 ? "Most indicators in commercial TI feeds overlap heavily with free/open sources, raising doubts about their unique value." :
                        index === 1 ? "Commercial feeds often focus on volume, but many indicators are low-quality or short-lived, which reduces their operational impact." :
                        "While raw feeds may not provide much tactical advantage, curated or contextualized intelligence can be more beneficial."
                      }
                      rows={3}
                      className="border-gray-200 dark:border-gray-700 resize-none text-sm leading-relaxed"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Zotero Sync Dialog */}
      <ZoteroSyncDialog
        open={showZoteroSyncDialog}
        onOpenChange={setShowZoteroSyncDialog}
        paperId={paperId}
        paperTitle={paper?.title || 'Unknown Paper'}
        onSyncSuccess={(result) => {
          setZoteroSyncStatus('success')
          toast({
            title: "Synced to Zotero",
            description: result.message || "Paper synced successfully to Zotero",
            duration: 6000 // Show longer so user can read the destination
          })

          // Update paper with sync information
          setPaper(prev => prev ? {
            ...prev,
            zoteroSyncStatus: 'synced',
            zoteroLastSynced: new Date().toISOString()
          } : null)

          // Reset status after 3 seconds
          setTimeout(() => setZoteroSyncStatus('idle'), 3000)
        }}
        onSyncError={(error) => {
          setZoteroSyncStatus('error')
          toast({
            title: "Sync failed",
            description: error,
            variant: "destructive"
          })

          // Reset status after 5 seconds
          setTimeout(() => setZoteroSyncStatus('idle'), 5000)
        }}
      />

      {/* Zotero User Selection Dialog (New Sync Flow) */}
      <ZoteroUserSelectionDialog
        open={showZoteroUserSelectionDialog}
        onOpenChange={setShowZoteroUserSelectionDialog}
        paperId={paperId}
        paperTitle={paper?.title || 'Unknown Paper'}
        onSyncSuccess={(result) => {
          setZoteroSyncStatus('success')
          toast({
            title: "Synced to Zotero",
            description: result.message || "Paper synced successfully to Zotero",
            duration: 6000
          })

          // Update paper with sync information
          setPaper(prev => prev ? {
            ...prev,
            zoteroSyncStatus: 'synced',
            zoteroLastSynced: new Date().toISOString()
          } : null)

          // Reset status after 3 seconds
          setTimeout(() => setZoteroSyncStatus('idle'), 3000)
        }}
        onSyncError={(error) => {
          setZoteroSyncStatus('error')
          toast({
            title: "Sync failed",
            description: error,
            variant: "destructive"
          })

          // Reset status after 5 seconds
          setTimeout(() => setZoteroSyncStatus('idle'), 5000)
        }}
      />
    </div>
  )
}
