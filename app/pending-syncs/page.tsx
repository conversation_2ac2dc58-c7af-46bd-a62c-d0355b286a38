"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { authenticatedFetch } from "@/lib/utils"
import { Loader2, RefreshCw, CheckCircle, XCircle, AlertTriangle, FileText, Users, Library } from "lucide-react"
import { ZoteroUserSelectionDialog } from "@/components/zotero-user-selection-dialog"
import type { Paper } from "@/lib/types"

interface PendingSyncPaper extends Paper {
  selected?: boolean
}

export default function PendingSyncsPage() {
  const [papers, setPapers] = useState<PendingSyncPaper[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isBulkSyncing, setIsBulkSyncing] = useState(false)
  const [selectedPapers, setSelectedPapers] = useState<Set<string>>(new Set())
  const [syncProgress, setSyncProgress] = useState<{ current: number; total: number } | null>(null)
  const [showUserSelectionDialog, setShowUserSelectionDialog] = useState(false)
  const [currentSyncPaper, setCurrentSyncPaper] = useState<PendingSyncPaper | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchPendingSyncs()
  }, [])

  const fetchPendingSyncs = async () => {
    setIsLoading(true)
    try {
      const response = await authenticatedFetch('/api/papers/pending-syncs')
      if (response.ok) {
        const data = await response.json()
        setPapers(data.data || [])
      } else {
        toast({
          title: "Failed to load pending syncs",
          description: "Could not fetch papers pending Zotero sync",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Network error",
        description: "Failed to connect to server",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const togglePaperSelection = (paperId: string) => {
    const newSelected = new Set(selectedPapers)
    if (newSelected.has(paperId)) {
      newSelected.delete(paperId)
    } else {
      newSelected.add(paperId)
    }
    setSelectedPapers(newSelected)
  }

  const toggleSelectAll = () => {
    if (selectedPapers.size === papers.length) {
      setSelectedPapers(new Set())
    } else {
      setSelectedPapers(new Set(papers.map(p => p.id)))
    }
  }

  const syncSinglePaper = async (paper: PendingSyncPaper): Promise<boolean> => {
    try {
      const response = await authenticatedFetch(`/api/papers/${paper.id}/sync-zotero`, {
        method: 'POST'
      })

      const data = await response.json()

      if (response.ok) {
        // Success - remove from pending list
        setPapers(prev => prev.filter(p => p.id !== paper.id))
        toast({
          title: "Synced successfully",
          description: `"${paper.title}" synced to Zotero`,
          duration: 3000
        })
        return true
      } else if (response.status === 409 && data.details?.requiresUserSelection) {
        // Requires user selection - show dialog
        setCurrentSyncPaper(paper)
        setShowUserSelectionDialog(true)
        return false // Don't count as completed yet
      } else {
        // Error
        const errorMessage = data.errors?.[0] || data.error || 'Sync failed'
        toast({
          title: "Sync failed",
          description: `"${paper.title}": ${errorMessage}`,
          variant: "destructive",
          duration: 5000
        })
        return false
      }
    } catch (error) {
      toast({
        title: "Network error",
        description: `Failed to sync "${paper.title}"`,
        variant: "destructive"
      })
      return false
    }
  }

  const bulkSync = async () => {
    if (selectedPapers.size === 0) {
      toast({
        title: "No papers selected",
        description: "Please select papers to sync",
        variant: "destructive"
      })
      return
    }

    setIsBulkSyncing(true)
    setSyncProgress({ current: 0, total: selectedPapers.size })

    const selectedPapersList = papers.filter(p => selectedPapers.has(p.id))
    let completed = 0

    for (const paper of selectedPapersList) {
      const success = await syncSinglePaper(paper)
      if (success) {
        completed++
      }
      setSyncProgress({ current: completed, total: selectedPapers.size })
      
      // Small delay between syncs to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setIsBulkSyncing(false)
    setSyncProgress(null)
    setSelectedPapers(new Set())

    toast({
      title: "Bulk sync completed",
      description: `${completed} of ${selectedPapers.size} papers synced successfully`,
      duration: 5000
    })
  }

  const bulkSkip = async () => {
    if (selectedPapers.size === 0) {
      toast({
        title: "No papers selected",
        description: "Please select papers to skip",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await authenticatedFetch('/api/papers/bulk-skip-sync', {
        method: 'POST',
        body: JSON.stringify({
          paperIds: Array.from(selectedPapers)
        })
      })

      if (response.ok) {
        // Remove skipped papers from the list
        setPapers(prev => prev.filter(p => !selectedPapers.has(p.id)))
        setSelectedPapers(new Set())
        
        toast({
          title: "Papers skipped",
          description: `${selectedPapers.size} papers marked as skipped`,
          duration: 3000
        })
      } else {
        toast({
          title: "Failed to skip papers",
          description: "Could not update paper sync status",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Network error",
        description: "Failed to skip papers",
        variant: "destructive"
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const truncateTitle = (title: string, maxLength: number = 80) => {
    return title.length > maxLength ? `${title.substring(0, maxLength)}...` : title
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading pending syncs...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Pending Zotero Syncs</h1>
        <p className="text-muted-foreground">
          Papers that haven't been synced to Zotero yet. Select papers to sync or skip them.
        </p>
      </div>

      {papers.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
            <p className="text-muted-foreground text-center">
              All your papers are synced to Zotero. New papers will appear here when they need syncing.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Checkbox
                checked={selectedPapers.size === papers.length && papers.length > 0}
                onCheckedChange={toggleSelectAll}
                disabled={isBulkSyncing}
              />
              <span className="text-sm text-muted-foreground">
                {selectedPapers.size} of {papers.length} selected
              </span>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchPendingSyncs}
                disabled={isBulkSyncing}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                variant="outline"
                onClick={bulkSkip}
                disabled={selectedPapers.size === 0 || isBulkSyncing}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Skip Selected
              </Button>
              <Button
                onClick={bulkSync}
                disabled={selectedPapers.size === 0 || isBulkSyncing}
              >
                {isBulkSyncing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Library className="h-4 w-4 mr-2" />
                )}
                Sync Selected
              </Button>
            </div>
          </div>

          {syncProgress && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Syncing papers: {syncProgress.current} of {syncProgress.total} completed
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            {papers.map((paper) => (
              <Card key={paper.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <Checkbox
                      checked={selectedPapers.has(paper.id)}
                      onCheckedChange={() => togglePaperSelection(paper.id)}
                      disabled={isBulkSyncing}
                      className="mt-1"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-lg leading-tight mb-2">
                            {truncateTitle(paper.title)}
                          </h3>
                          
                          {paper.authors && paper.authors.length > 0 && (
                            <p className="text-sm text-muted-foreground mb-2">
                              {paper.authors.join(', ')}
                            </p>
                          )}
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {paper.venue && (
                              <span>{paper.venue}</span>
                            )}
                            {paper.year && (
                              <span>{paper.year}</span>
                            )}
                            <span>Added {formatDate(paper.createdAt)}</span>
                          </div>
                        </div>
                        
                        <div className="flex flex-col items-end gap-2">
                          <Badge variant="secondary">
                            <FileText className="h-3 w-3 mr-1" />
                            Pending Sync
                          </Badge>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => syncSinglePaper(paper)}
                            disabled={isBulkSyncing}
                          >
                            <Library className="h-4 w-4 mr-2" />
                            Sync Now
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}

      {/* User Selection Dialog */}
      {currentSyncPaper && (
        <ZoteroUserSelectionDialog
          open={showUserSelectionDialog}
          onOpenChange={setShowUserSelectionDialog}
          paperId={currentSyncPaper.id}
          paperTitle={currentSyncPaper.title}
          onSyncSuccess={(result) => {
            // Remove from pending list
            setPapers(prev => prev.filter(p => p.id !== currentSyncPaper.id))
            setCurrentSyncPaper(null)
            
            toast({
              title: "Synced successfully",
              description: result.message || "Paper synced to Zotero",
              duration: 3000
            })
          }}
          onSyncError={(error) => {
            setCurrentSyncPaper(null)
            toast({
              title: "Sync failed",
              description: error,
              variant: "destructive"
            })
          }}
        />
      )}
    </div>
  )
}
