"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, <PERSON><PERSON><PERSON>, Book<PERSON>pen<PERSON>heck, Presentation, Trash2, Clock, Play, Grid3X3, LayoutGrid } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ConditionalSidebarTrigger } from "@/components/ui/conditional-sidebar-trigger"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/hooks/use-toast"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthModal } from "@/components/auth/AuthModal"
import { authenticatedFetch } from "@/lib/utils"
import { ZoteroDestinationSelector } from "@/components/zotero-destination-selector"
import type { Collection } from "@/lib/types"

function CollectionsPageContent() {
  const [collections, setCollections] = useState<Collection[]>([])
  const [newCollectionName, setNewCollectionName] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [viewMode, setViewMode] = useState<'card' | 'compact'>('card')
  const [reviewStatuses, setReviewStatuses] = useState<Record<string, {
    inReview: number
    total: number
    percentage: number
    due: number
    scheduled: number
    overdue: number
    reviewed: number
  }>>({})
  const { toast } = useToast()

  useEffect(() => {
    fetchCollections()
  }, [])

  useEffect(() => {
    if (collections.length > 0) {
      fetchAllReviewStatuses()
    }
  }, [collections])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "n":
          e.preventDefault()
          setIsDialogOpen(true)
          break
        case "escape":
          e.preventDefault()
          setIsDialogOpen(false)
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  const fetchCollections = async () => {
    try {
      const response = await authenticatedFetch("/api/collections")
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      // Handle wrapped response format from createSuccessResponse and createListResponse
      const collections = data.data?.data || data.data || data || []
      setCollections(collections)
    } catch (error) {
      console.error("Failed to fetch collections:", error)
      setCollections([])
    }
  }

  const fetchAllReviewStatuses = async () => {
    try {
      const statuses = await Promise.all(
        collections.map(async (collection) => {
          try {
            const response = await authenticatedFetch(`/api/collections/${collection.id}/review`)
            if (response.ok) {
              const data = await response.json()
              return {
                id: collection.id,
                status: {
                  inReview: data.inReview,
                  total: data.totalPapers,
                  percentage: data.progress.percentage,
                  due: data.due,
                  scheduled: data.scheduled || 0,
                  overdue: data.overdue || 0,
                  reviewed: data.reviewed || 0
                }
              }
            }
            return { id: collection.id, status: null }
          } catch (error) {
            console.error(`Failed to fetch review status for collection ${collection.id}:`, error)
            return { id: collection.id, status: null }
          }
        })
      )

      const statusMap = statuses.reduce((acc, { id, status }) => {
        if (status) {
          acc[id] = status
        }
        return acc
      }, {} as Record<string, any>)

      setReviewStatuses(statusMap)
    } catch (error) {
      console.error("Failed to fetch review statuses:", error)
    }
  }

  const createCollection = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newCollectionName.trim()) return

    try {
      const response = await authenticatedFetch("/api/collections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: newCollectionName }),
      })

      if (response.ok) {
        toast({ title: "Collection created" })
        setNewCollectionName("")
        setIsDialogOpen(false)
        fetchCollections()
      } else {
        const errorData = await response.json()
        toast({
          title: "Failed to create collection",
          description: errorData.error || "Unknown error",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({ title: "Failed to create collection", variant: "destructive" })
    }
  }

  const deleteCollection = async (collectionId: string, collectionName: string) => {
    if (!confirm(`Are you sure you want to delete "${collectionName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await authenticatedFetch(`/api/collections/${collectionId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Collection deleted successfully" })
        fetchCollections() // Refresh the list
      } else {
        throw new Error("Failed to delete collection")
      }
    } catch (error) {
      console.error("Failed to delete collection:", error)
      toast({ title: "Failed to delete collection", variant: "destructive" })
    }
  }

  const markCollectionForReview = async (id: string, name: string) => {
    try {
      const response = await authenticatedFetch(`/api/collections/${id}/review`, {
        method: "POST",
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Collection marked for review",
          description: `${data.successful} papers added to review queue`
        })
        // Refresh review statuses to show updated state
        fetchAllReviewStatuses()
      } else {
        const errorData = await response.json()
        toast({
          title: "Failed to mark collection for review",
          description: errorData.error || "Unknown error",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to mark collection for review:", error)
      toast({
        title: "Failed to mark collection for review",
        description: "Network error",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <ConditionalSidebarTrigger />
          <h1 className="text-2xl font-bold">Collections</h1>
          <div className="flex-1" />
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'card' ? 'compact' : 'card')}
              title={`Switch to ${viewMode === 'card' ? 'compact' : 'card'} view`}
            >
              {viewMode === 'card' ? (
                <Grid3X3 className="h-4 w-4" />
              ) : (
                <LayoutGrid className="h-4 w-4" />
              )}
            </Button>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Collection
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Collection</DialogTitle>
              </DialogHeader>
              <form onSubmit={createCollection} className="space-y-4">
                <Input
                  value={newCollectionName}
                  onChange={(e) => setNewCollectionName(e.target.value)}
                  placeholder="Collection name..."
                  autoFocus
                />

                {/* Collection-specific Zotero sync has been replaced with the new unified sync flow */}

                <Button type="submit" className="w-full">
                  Create Collection
                </Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-4">
        {viewMode === 'card' ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {collections.map((collection) => (
            <Card
              key={collection.id}
              className="hover:shadow-md transition-shadow focus-within:ring-2 focus-within:ring-blue-500"
              tabIndex={0}
              onKeyDown={(e) => {
                switch (e.key.toLowerCase()) {
                  case "enter":
                    e.preventDefault()
                    window.location.href = `/collections/${collection.id}`
                    break
                  case "p":
                    e.preventDefault()
                    window.location.href = `/present/${collection.id}`
                    break
                  case "delete":
                  case "backspace":
                    if (e.shiftKey) {
                      e.preventDefault()
                      deleteCollection(collection.id, collection.name)
                    }
                    break
                }
              }}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {reviewStatuses[collection.id]?.inReview > 0 ? (
                      <BookOpenCheck className="h-5 w-5 text-green-600" title="Collection has papers in review" />
                    ) : (
                      <BookOpen className="h-5 w-5" title="Collection" />
                    )}
                    {collection.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteCollection(collection.id, collection.name)
                    }}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    title="Delete collection"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                {reviewStatuses[collection.id]?.inReview > 0 && (
                  <div className="mt-2 text-sm">
                    <div className="flex items-center justify-between">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="cursor-help text-muted-foreground">
                            {reviewStatuses[collection.id].inReview} of {reviewStatuses[collection.id].total} in review system
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-xs space-y-1">
                            <div>✅ {reviewStatuses[collection.id].reviewed || 0} reviewed (scheduled for future)</div>
                            <div>🔁 {reviewStatuses[collection.id].scheduled || 0} scheduled</div>
                            <div>❌ {reviewStatuses[collection.id].overdue || 0} overdue</div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                      {reviewStatuses[collection.id].due > 0 ? (
                        <span className="text-orange-600 font-medium">
                          {reviewStatuses[collection.id].due} due now
                        </span>
                      ) : (
                        <span className="text-green-600 font-medium">
                          ✓ Up to date
                        </span>
                      )}
                    </div>
                    <div className="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                      <div
                        className="bg-green-600 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${reviewStatuses[collection.id].percentage}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-muted-foreground">{collection.paperIds.length} papers</p>
                  {/* Collection-specific Zotero sync indicators removed - using unified sync flow */}
                </div>
                <div className="flex gap-2">
                  <Link href={`/collections/${collection.id}`} className="flex-1">
                    <Button variant="outline" className="w-full bg-transparent">
                      View
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      markCollectionForReview(collection.id, collection.name)
                    }}
                    title="Mark all papers for review"
                  >
                    <Clock className="h-4 w-4" />
                  </Button>
                  {reviewStatuses[collection.id]?.due > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.location.href = `/review/session?collection=${collection.id}`
                      }}
                      title={`Start review session (${reviewStatuses[collection.id]?.due} papers due)`}
                      className="bg-green-50 hover:bg-green-100 border-green-200"
                    >
                      <Play className="h-4 w-4 text-green-600" />
                    </Button>
                  )}
                  <Link href={`/present/${collection.id}`}>
                    <Button variant="outline" size="sm" title="Present collection">
                      <Presentation className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="grid grid-cols-12 gap-4 p-3 text-sm font-medium text-muted-foreground border-b">
              <div className="col-span-4">Name</div>
              <div className="col-span-2">Papers</div>
              <div className="col-span-3">Review Status</div>
              <div className="col-span-3">Actions</div>
            </div>
            {collections.map((collection) => (
              <div
                key={collection.id}
                className="grid grid-cols-12 gap-4 p-3 hover:bg-muted/50 rounded-lg transition-colors focus-within:ring-2 focus-within:ring-blue-500"
                tabIndex={0}
                onKeyDown={(e) => {
                  switch (e.key.toLowerCase()) {
                    case "enter":
                      e.preventDefault()
                      window.location.href = `/collections/${collection.id}`
                      break
                    case "p":
                      e.preventDefault()
                      window.location.href = `/present/${collection.id}`
                      break
                    case "delete":
                    case "backspace":
                      if (e.shiftKey) {
                        e.preventDefault()
                        deleteCollection(collection.id, collection.name)
                      }
                      break
                  }
                }}
              >
                <div className="col-span-4">
                  <div className="font-medium">{collection.name}</div>
                </div>
                <div className="col-span-2 flex items-center">
                  <span className="text-sm">{collection.paperIds.length}</span>
                </div>
                <div className="col-span-3 flex items-center">
                  {reviewStatuses[collection.id]?.inReview > 0 ? (
                    <div className="text-sm">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="cursor-help">
                            {reviewStatuses[collection.id].inReview} of {reviewStatuses[collection.id].total} in review system
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-xs space-y-1">
                            <div>✅ {reviewStatuses[collection.id].reviewed || 0} reviewed (scheduled for future)</div>
                            <div>🔁 {reviewStatuses[collection.id].scheduled || 0} scheduled</div>
                            <div>❌ {reviewStatuses[collection.id].overdue || 0} overdue</div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                      {reviewStatuses[collection.id].due > 0 && (
                        <div className="text-orange-600 font-medium text-xs">
                          {reviewStatuses[collection.id].due} due
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">No reviews</span>
                  )}
                </div>
                <div className="col-span-3 flex items-center gap-1">
                  <Link href={`/collections/${collection.id}`}>
                    <Button variant="outline" size="sm" title="View collection">
                      <BookOpen className="h-3 w-3" />
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      markCollectionForReview(collection.id, collection.name)
                    }}
                    title="Mark all papers for review"
                  >
                    <Clock className="h-3 w-3" />
                  </Button>
                  {reviewStatuses[collection.id]?.due > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.location.href = `/review/session?collection=${collection.id}`
                      }}
                      title={`Start review session (${reviewStatuses[collection.id]?.due} papers due)`}
                      className="bg-green-50 hover:bg-green-100 border-green-200"
                    >
                      <Play className="h-3 w-3 text-green-600" />
                    </Button>
                  )}
                  <Link href={`/present/${collection.id}`}>
                    <Button variant="outline" size="sm" title="Present collection">
                      <Presentation className="h-3 w-3" />
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteCollection(collection.id, collection.name)
                    }}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    title="Delete collection"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default function CollectionsPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <CollectionsPageContent />
    </ProtectedRoute>
  )
}
