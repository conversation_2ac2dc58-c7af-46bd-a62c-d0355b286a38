-- Migration: Zotero Sync Refactor - Add group ID and new sync statuses
-- This migration supports the new simplified Zotero sync flow

-- Add zotero_group_id field to papers table
ALTER TABLE papers 
ADD COLUMN IF NOT EXISTS zotero_group_id VARCHAR(255);

-- Update constraint for zotero_sync_status to include new statuses
DO $$ 
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'papers_zotero_sync_status_check'
    ) THEN
        ALTER TABLE papers DROP CONSTRAINT papers_zotero_sync_status_check;
    END IF;
    
    -- Add new constraint with additional statuses
    ALTER TABLE papers 
    ADD CONSTRAINT papers_zotero_sync_status_check 
    CHECK (zotero_sync_status IN ('not_synced', 'synced', 'error', 'pending', 'mapped', 'skipped'));
END $$;

-- Create index for zotero_group_id
CREATE INDEX IF NOT EXISTS idx_papers_zotero_group_id ON papers(zotero_group_id);

-- Add comments for documentation
COMMENT ON COLUMN papers.zotero_group_id IS 'Zotero group ID where this paper is synced. NULL for user library';
COMMENT ON COLUMN papers.zotero_sync_status IS 'Sync status: not_synced, pending, mapped, synced, skipped, error';

-- Note: Existing papers will keep their current sync status
-- The new sync flow will handle migration of existing synced papers
