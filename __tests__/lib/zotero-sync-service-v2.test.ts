/**
 * Tests for ZoteroSyncServiceV2 - New simplified sync flow
 */

import { ZoteroSyncServiceV2 } from '@/lib/zotero-sync-service-v2'
import { ZoteroClient } from '@/lib/zotero-client'
import { papers, notes } from '@/lib/database'
import { getFeatureFlag } from '@/lib/config'

// Mock dependencies
jest.mock('@/lib/zotero-client')
jest.mock('@/lib/database')
jest.mock('@/lib/config')

const mockZoteroClient = ZoteroClient as jest.MockedClass<typeof ZoteroClient>
const mockPapers = papers as jest.Mocked<typeof papers>
const mockNotes = notes as jest.Mocked<typeof notes>
const mockGetFeatureFlag = getFeatureFlag as jest.MockedFunction<typeof getFeatureFlag>

describe('ZoteroSyncServiceV2', () => {
  let syncService: ZoteroSyncServiceV2
  let mockClient: jest.Mocked<ZoteroClient>

  const mockSettings = {
    enabled: true,
    apiKey: 'test-api-key',
    userId: 'test-user-id',
    libraryType: 'user' as const,
    libraryId: undefined
  }

  const mockPaper = {
    id: 'paper-1',
    title: 'Test Paper',
    authors: ['Author 1'],
    doi: '10.1000/test',
    userId: 'user-1',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z',
    tags: [],
    starred: false,
    paperType: 'journalArticle' as const
  }

  const mockNote = {
    id: 'note-1',
    paperId: 'paper-1',
    quickSummary: 'Test summary',
    keyIdeas: ['Idea 1', 'Idea 2']
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock feature flag to enable new sync flow
    mockGetFeatureFlag.mockReturnValue(true)
    
    // Create mock client instance
    mockClient = {
      createOrUpdateNote: jest.fn(),
      findItemByDOI: jest.fn(),
      findItemByTitle: jest.fn(),
      createOrUpdateItem: jest.fn(),
      getUserLibraries: jest.fn()
    } as any

    // Mock ZoteroClient constructor
    mockZoteroClient.mockImplementation(() => mockClient)
    
    syncService = new ZoteroSyncServiceV2('test-api-key', mockSettings)
  })

  describe('syncPaper', () => {
    it('should throw error if new sync flow is disabled', async () => {
      mockGetFeatureFlag.mockReturnValue(false)
      
      await expect(syncService.syncPaper('paper-1')).rejects.toThrow(
        'New Zotero sync flow is not enabled'
      )
    })

    it('should short-circuit if paper already has mapping', async () => {
      // Mock paper with existing mapping
      const paperWithMapping = {
        ...mockPaper,
        zoteroItemKey: 'existing-item-key',
        zoteroGroupId: null // user library
      }
      
      mockPapers.getById.mockResolvedValue(paperWithMapping)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      mockClient.createOrUpdateNote.mockResolvedValue({
        success: true,
        noteKey: 'updated-note-key'
      })
      mockPapers.update.mockResolvedValue(paperWithMapping)

      const result = await syncService.syncPaper('paper-1')

      expect(result.success).toBe(true)
      expect(result.telemetry?.cacheHits).toBe(1)
      expect(result.telemetry?.apiCalls).toBe(1)
      expect(mockClient.createOrUpdateNote).toHaveBeenCalledWith(
        'user',
        '',
        'existing-item-key',
        mockNote,
        undefined
      )
    })

    it('should search in default group if no mapping exists', async () => {
      mockPapers.getById.mockResolvedValue(mockPaper)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      
      // Mock finding existing item by DOI
      mockClient.findItemByDOI.mockResolvedValue({
        key: 'found-item-key',
        version: 1
      })
      
      mockClient.createOrUpdateNote.mockResolvedValue({
        success: true,
        noteKey: 'new-note-key'
      })
      
      mockPapers.update.mockResolvedValue(mockPaper)

      const result = await syncService.syncPaper('paper-1')

      expect(result.success).toBe(true)
      expect(result.telemetry?.searchAttempts).toBe(1)
      expect(result.telemetry?.apiCalls).toBe(2) // findItemByDOI + createOrUpdateNote
      expect(mockClient.findItemByDOI).toHaveBeenCalledWith('user', '', '10.1000/test')
      expect(mockPapers.update).toHaveBeenCalledWith('paper-1', {
        zoteroItemKey: 'found-item-key',
        zoteroNoteKey: 'new-note-key',
        zoteroGroupId: null,
        zoteroLastSynced: expect.any(String),
        zoteroSyncStatus: 'mapped'
      })
    })

    it('should fallback to title search if DOI search fails', async () => {
      mockPapers.getById.mockResolvedValue(mockPaper)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      
      // Mock DOI search failing, title search succeeding
      mockClient.findItemByDOI.mockResolvedValue(null)
      mockClient.findItemByTitle.mockResolvedValue({
        key: 'found-by-title-key',
        version: 1
      })
      
      mockClient.createOrUpdateNote.mockResolvedValue({
        success: true,
        noteKey: 'new-note-key'
      })
      
      mockPapers.update.mockResolvedValue(mockPaper)

      const result = await syncService.syncPaper('paper-1')

      expect(result.success).toBe(true)
      expect(mockClient.findItemByDOI).toHaveBeenCalled()
      expect(mockClient.findItemByTitle).toHaveBeenCalledWith('user', '', 'Test Paper', undefined)
    })

    it('should return requiresUserSelection if paper not found in default group', async () => {
      mockPapers.getById.mockResolvedValue(mockPaper)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      
      // Mock both searches failing
      mockClient.findItemByDOI.mockResolvedValue(null)
      mockClient.findItemByTitle.mockResolvedValue(null)

      const result = await syncService.syncPaper('paper-1')

      expect(result.success).toBe(false)
      expect(result.requiresUserSelection).toBe(true)
      expect(result.searchedInDefaultGroup).toBe(true)
      expect(result.error).toBe('Paper not found in default group - user selection required')
    })
  })

  describe('createInGroup', () => {
    it('should create new item and note in specified group', async () => {
      mockPapers.getById.mockResolvedValue(mockPaper)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      
      mockClient.createOrUpdateItem.mockResolvedValue({
        success: true,
        itemKey: 'new-item-key'
      })
      
      mockClient.createOrUpdateNote.mockResolvedValue({
        success: true,
        noteKey: 'new-note-key'
      })
      
      mockPapers.update.mockResolvedValue(mockPaper)

      const result = await syncService.createInGroup('paper-1', 'group', 'group-123')

      expect(result.success).toBe(true)
      expect(result.itemKey).toBe('new-item-key')
      expect(result.noteKey).toBe('new-note-key')
      expect(mockClient.createOrUpdateItem).toHaveBeenCalledWith('group', 'group-123', mockPaper)
      expect(mockPapers.update).toHaveBeenCalledWith('paper-1', {
        zoteroItemKey: 'new-item-key',
        zoteroNoteKey: 'new-note-key',
        zoteroGroupId: 'group-123',
        zoteroLastSynced: expect.any(String),
        zoteroSyncStatus: 'synced'
      })
    })

    it('should handle user library creation', async () => {
      mockPapers.getById.mockResolvedValue(mockPaper)
      mockNotes.getByPaperId.mockResolvedValue(mockNote)
      
      mockClient.createOrUpdateItem.mockResolvedValue({
        success: true,
        itemKey: 'new-item-key'
      })
      
      mockClient.createOrUpdateNote.mockResolvedValue({
        success: true,
        noteKey: 'new-note-key'
      })
      
      mockPapers.update.mockResolvedValue(mockPaper)

      const result = await syncService.createInGroup('paper-1', 'user')

      expect(result.success).toBe(true)
      expect(mockPapers.update).toHaveBeenCalledWith('paper-1', {
        zoteroItemKey: 'new-item-key',
        zoteroNoteKey: 'new-note-key',
        zoteroGroupId: null, // null for user library
        zoteroLastSynced: expect.any(String),
        zoteroSyncStatus: 'synced'
      })
    })
  })

  describe('getAvailableGroups', () => {
    it('should return filtered group libraries', async () => {
      const mockLibraries = [
        { id: 'user', name: 'My Library', type: 'user' },
        { id: 'group-1', name: 'Research Group', type: 'group' },
        { id: 'group-2', name: 'Lab Group', type: 'group' }
      ]
      
      mockClient.getUserLibraries.mockResolvedValue(mockLibraries)

      const result = await syncService.getAvailableGroups()

      expect(result).toEqual([
        { id: 'group-1', name: 'Research Group', type: 'group' },
        { id: 'group-2', name: 'Lab Group', type: 'group' }
      ])
    })

    it('should handle errors gracefully', async () => {
      mockClient.getUserLibraries.mockRejectedValue(new Error('API error'))

      const result = await syncService.getAvailableGroups()

      expect(result).toEqual([])
    })
  })
})
