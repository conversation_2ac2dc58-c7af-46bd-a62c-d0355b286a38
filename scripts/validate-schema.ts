#!/usr/bin/env npx tsx

/**
 * Database Schema Validation CLI Tool
 * Validates the database schema and reports any discrepancies
 */

import { validateDatabaseSchema, quickSchemaValidation } from '../lib/schema-validator'

async function main() {
  const args = process.argv.slice(2)
  const isQuick = args.includes('--quick') || args.includes('-q')
  const isVerbose = args.includes('--verbose') || args.includes('-v')

  console.log('🔍 PaperNugget Database Schema Validation')
  console.log('==========================================')
  console.log('')

  try {
    if (isQuick) {
      console.log('Running quick schema validation...')
      const result = await quickSchemaValidation()
      
      if (result.isValid) {
        console.log('✅ Quick schema validation passed')
        console.log('   All critical tables and columns are present')
      } else {
        console.log('❌ Quick schema validation failed')
        console.log('')
        console.log('Errors:')
        result.errors.forEach(error => console.log(`   - ${error}`))
        process.exit(1)
      }
    } else {
      console.log('Running comprehensive schema validation...')
      const result = await validateDatabaseSchema()
      
      console.log('')
      console.log(`Schema Validation Results:`)
      console.log(`=========================`)
      console.log(`Overall Status: ${result.isValid ? '✅ VALID' : '❌ INVALID'}`)
      console.log(`Errors: ${result.errors.length}`)
      console.log(`Warnings: ${result.warnings.length}`)
      console.log('')

      if (result.errors.length > 0) {
        console.log('❌ Errors:')
        result.errors.forEach(error => console.log(`   - ${error}`))
        console.log('')
      }

      if (result.warnings.length > 0) {
        console.log('⚠️  Warnings:')
        result.warnings.forEach(warning => console.log(`   - ${warning}`))
        console.log('')
      }

      if (isVerbose) {
        console.log('📊 Detailed Results:')
        console.log('')
        
        // Table validation details
        console.log('Tables:')
        for (const table of result.details.tables) {
          const status = table.exists ? '✅' : '❌'
          console.log(`   ${status} ${table.tableName}`)
          
          if (table.exists) {
            if (table.missingColumns.length > 0) {
              console.log(`      Missing columns: ${table.missingColumns.join(', ')}`)
            }
            if (table.extraColumns.length > 0) {
              console.log(`      Extra columns: ${table.extraColumns.join(', ')}`)
            }
          }
        }
        
        console.log('')
        
        // Index validation details
        console.log('Indexes:')
        const missingIndexes = result.details.indexes.filter(idx => !idx.exists)
        const existingIndexes = result.details.indexes.filter(idx => idx.exists)
        
        console.log(`   ✅ Existing: ${existingIndexes.length}`)
        console.log(`   ❌ Missing: ${missingIndexes.length}`)
        
        if (missingIndexes.length > 0 && isVerbose) {
          console.log('   Missing indexes:')
          missingIndexes.forEach(idx => console.log(`      - ${idx.indexName}`))
        }
      }

      if (!result.isValid) {
        process.exit(1)
      }
    }

    console.log('')
    console.log('🎉 Schema validation completed successfully!')
    
  } catch (error) {
    console.error('❌ Schema validation failed:', error)
    process.exit(1)
  }
}

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('PaperNugget Database Schema Validation Tool')
  console.log('')
  console.log('Usage:')
  console.log('  npx tsx scripts/validate-schema.ts [options]')
  console.log('')
  console.log('Options:')
  console.log('  -q, --quick     Run quick validation (critical tables/columns only)')
  console.log('  -v, --verbose   Show detailed validation results')
  console.log('  -h, --help      Show this help message')
  console.log('')
  console.log('Examples:')
  console.log('  npx tsx scripts/validate-schema.ts              # Full validation')
  console.log('  npx tsx scripts/validate-schema.ts --quick      # Quick validation')
  console.log('  npx tsx scripts/validate-schema.ts --verbose    # Full with details')
  process.exit(0)
}

// Run the main function
main().catch(error => {
  console.error('Unexpected error:', error)
  process.exit(1)
})
