#!/bin/bash

# PaperNugget Database Backup and Restore Script
# This script provides safe backup and restore functionality

set -e

BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if Docker Compose is running
check_docker_compose() {
    if ! docker compose ps db | grep -q "Up"; then
        log_error "Database container is not running. Please start with 'make start' first."
        exit 1
    fi
}

# Function to create backup
create_backup() {
    log_info "Creating database backup..."

    # Validate schema before backup
    log_info "Validating database schema before backup..."
    if ! docker compose exec -T app npx tsx scripts/validate-schema.ts --quick; then
        log_warning "Schema validation failed - backup will continue but may contain inconsistencies"
    else
        log_success "Schema validation passed"
    fi

    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"

    # Create data-only backup with INSERT statements (safer for restore)
    log_info "Creating data-only backup..."
    docker compose exec -T db pg_dump \
        -U papernugget \
        --data-only \
        --inserts \
        --column-inserts \
        --disable-triggers \
        papernugget > "$BACKUP_DIR/backup_data_$DATE.sql"

    # Create full backup (schema + data)
    log_info "Creating full backup..."
    docker compose exec -T db pg_dump \
        -U papernugget \
        papernugget > "$BACKUP_DIR/backup_full_$DATE.sql"

    # Create schema-only backup
    log_info "Creating schema-only backup..."
    docker compose exec -T db pg_dump \
        -U papernugget \
        --schema-only \
        papernugget > "$BACKUP_DIR/backup_schema_$DATE.sql"

    # Create metadata file with backup information
    log_info "Creating backup metadata..."
    cat > "$BACKUP_DIR/backup_metadata_$DATE.json" << EOF
{
    "timestamp": "$DATE",
    "created_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "database_version": "$(docker compose exec -T db psql -U papernugget -d papernugget -t -c 'SELECT version();' | head -1 | xargs)",
    "schema_validated": true,
    "files": {
        "data_only": "backup_data_$DATE.sql",
        "full": "backup_full_$DATE.sql",
        "schema_only": "backup_schema_$DATE.sql"
    }
}
EOF

    log_success "Backups created:"
    log_success "  Data only: $BACKUP_DIR/backup_data_$DATE.sql"
    log_success "  Full: $BACKUP_DIR/backup_full_$DATE.sql"
    log_success "  Schema only: $BACKUP_DIR/backup_schema_$DATE.sql"
    log_success "  Metadata: $BACKUP_DIR/backup_metadata_$DATE.json"
}

# Function to list available backups
list_backups() {
    log_info "Available backup files:"
    if [ -d "$BACKUP_DIR" ] && [ "$(ls -A $BACKUP_DIR 2>/dev/null)" ]; then
        # Group backups by timestamp
        for metadata_file in "$BACKUP_DIR"/backup_metadata_*.json; do
            if [ -f "$metadata_file" ]; then
                timestamp=$(basename "$metadata_file" | sed 's/backup_metadata_\(.*\)\.json/\1/')
                created_at=$(grep '"created_at"' "$metadata_file" | cut -d'"' -f4)
                echo "  📦 Backup Set: $timestamp (Created: $created_at)"
                echo "     - Data: backup_data_$timestamp.sql"
                echo "     - Full: backup_full_$timestamp.sql"
                echo "     - Schema: backup_schema_$timestamp.sql"
                echo "     - Metadata: backup_metadata_$timestamp.json"
                echo ""
            fi
        done

        # Show any orphaned files
        orphaned_files=$(ls -1 "$BACKUP_DIR"/*.sql 2>/dev/null | grep -v -f <(ls -1 "$BACKUP_DIR"/backup_metadata_*.json 2>/dev/null | sed 's/backup_metadata_\(.*\)\.json/backup_.*_\1\.sql/') 2>/dev/null || true)
        if [ -n "$orphaned_files" ]; then
            echo "  🔍 Orphaned backup files (no metadata):"
            echo "$orphaned_files" | while read -r file; do
                echo "     - $(basename "$file")"
            done
        fi
    else
        log_warning "No backup files found in $BACKUP_DIR/"
    fi
}

# Function to restore from backup
restore_backup() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "Please specify a backup file to restore from"
        list_backups
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    log_warning "This will replace all existing data in the database!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled"
        exit 0
    fi
    
    log_info "Starting database restore from: $backup_file"
    
    # Stop the application to prevent conflicts
    log_info "Stopping application..."
    docker compose stop app
    
    # Determine backup type and restore accordingly
    if [[ "$backup_file" == *"data"* ]]; then
        restore_data_only "$backup_file"
    elif [[ "$backup_file" == *"full"* ]]; then
        restore_full "$backup_file"
    else
        # Try to detect backup type by content
        if grep -q "CREATE TABLE" "$backup_file"; then
            restore_full "$backup_file"
        else
            restore_data_only "$backup_file"
        fi
    fi
    
    # Start the application
    log_info "Starting application..."
    docker compose start app
    
    # Wait for application to be ready
    log_info "Waiting for application to be ready..."
    sleep 5
    
    # Verify restore
    if docker compose exec -T db psql -U papernugget -d papernugget -c "SELECT COUNT(*) FROM users;" >/dev/null 2>&1; then
        log_success "Database restored successfully!"
    else
        log_error "Database restore may have failed. Please check the logs."
        exit 1
    fi
}

# Function to restore data-only backup
restore_data_only() {
    local backup_file="$1"
    
    log_info "Restoring data-only backup..."
    
    # Clear existing data in correct order (respecting foreign keys)
    log_info "Clearing existing data..."
    docker compose exec -T db psql -U papernugget -d papernugget << 'EOF'
-- Disable triggers temporarily for faster deletion
SET session_replication_role = replica;

-- Delete in order to respect foreign key constraints
TRUNCATE TABLE audit_logs CASCADE;
TRUNCATE TABLE reviews CASCADE;
TRUNCATE TABLE notes CASCADE;
TRUNCATE TABLE papers CASCADE;
TRUNCATE TABLE collections CASCADE;
TRUNCATE TABLE password_reset_tokens CASCADE;
TRUNCATE TABLE password_history CASCADE;
TRUNCATE TABLE email_verification_tokens CASCADE;
TRUNCATE TABLE user_sessions CASCADE;
TRUNCATE TABLE users CASCADE;

-- Re-enable triggers
SET session_replication_role = DEFAULT;
EOF
    
    # Restore data
    log_info "Restoring data..."
    docker compose exec -T db psql -U papernugget -d papernugget < "$backup_file"

    # Validate schema after restore
    log_info "Validating database schema after restore..."
    if docker compose exec -T app npx tsx scripts/validate-schema.ts --quick; then
        log_success "Schema validation passed after data restore"
    else
        log_warning "Schema validation failed after data restore - manual intervention may be required"
    fi
}

# Function to restore full backup
restore_full() {
    local backup_file="$1"
    
    log_info "Restoring full backup..."
    
    # Drop and recreate database
    log_warning "Dropping and recreating database..."
    docker compose exec -T db psql -U papernugget -d postgres << 'EOF'
DROP DATABASE IF EXISTS papernugget;
CREATE DATABASE papernugget OWNER papernugget;
EOF
    
    # Restore full backup
    log_info "Restoring full backup..."
    docker compose exec -T db psql -U papernugget -d papernugget < "$backup_file"

    # Run migrations to ensure schema is up to date
    log_info "Running database migrations after restore..."
    docker compose exec -T app npm run migrate

    # Validate schema after restore
    log_info "Validating database schema after restore..."
    if docker compose exec -T app npx tsx scripts/validate-schema.ts --quick; then
        log_success "Schema validation passed after full restore"
    else
        log_warning "Schema validation failed after full restore - manual intervention may be required"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [backup|restore|list]"
    echo ""
    echo "Commands:"
    echo "  backup           Create a new backup"
    echo "  restore <file>   Restore from backup file"
    echo "  list             List available backup files"
    echo ""
    echo "Examples:"
    echo "  $0 backup"
    echo "  $0 restore backups/backup_data_20250815_173555.sql"
    echo "  $0 list"
}

# Main script logic
case "${1:-}" in
    backup)
        check_docker_compose
        create_backup
        ;;
    restore)
        check_docker_compose
        restore_backup "$2"
        ;;
    list)
        list_backups
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
