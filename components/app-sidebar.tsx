"use client"

import { BookOpen, FileText, RotateCcw, Plus, LogIn, Settings, Home, Shield, Users, Activity, BarChart3, HelpCircle, Database, ScrollText, Clock } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"

import { useAuth } from "@/lib/auth-context"
import { UserMenu } from "@/components/auth/UserMenu"
import { AuthModal } from "@/components/auth/AuthModal"
import { KofiSupport } from "@/components/ko-fi-support"
import { getLatestVersion, getRecentReleaseCount } from "@/lib/changelog-data"
import { Badge } from "@/components/ui/badge"

const menuItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "Papers",
    url: "/papers",
    icon: FileText,
  },
  {
    title: "Collections",
    url: "/collections",
    icon: BookOpen,
  },
  {
    title: "Pending Syncs",
    url: "/pending-syncs",
    icon: Clock,
  },
  {
    title: "Review",
    url: "/review",
    icon: RotateCcw,
  },
  {
    title: "Getting Started",
    url: "/getting-started",
    icon: HelpCircle,
  },
  {
    title: "Changelog",
    url: "/changelog",
    icon: ScrollText,
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
  },
]

const adminMenuItems = [
  {
    title: "Admin Dashboard",
    url: "/admin",
    icon: Shield,
  },
  {
    title: "User Management",
    url: "/admin/users",
    icon: Users,
  },
  {
    title: "Audit Logs",
    url: "/admin/audit",
    icon: Activity,
  },
  {
    title: "System Metrics",
    url: "/admin/metrics",
    icon: BarChart3,
  },
  {
    title: "Backup & Restore",
    url: "/admin/backup",
    icon: Database,
  },
]

export function AppSidebar() {
  const pathname = usePathname()
  const { isAuthenticated, isLoading, user } = useAuth()
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="px-2 py-2">
          <Link href="/">
            <h1 className="text-xl font-bold cursor-pointer hover:text-primary transition-colors">
              PaperNugget
            </h1>
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={pathname.startsWith(item.url)}>
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                      {item.title === "Changelog" && (
                        <div className="ml-auto flex items-center gap-1">
                          <Badge variant="secondary" className="text-xs">
                            v{getLatestVersion()}
                          </Badge>
                          {getRecentReleaseCount() > 0 && (
                            <Badge variant="default" className="bg-green-500 hover:bg-green-500 text-xs px-1">
                              New
                            </Badge>
                          )}
                        </div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        {/* Admin Menu Items - Only show for admin users */}
        {isAuthenticated && user?.role === 'admin' && (
          <SidebarGroup>
            <SidebarGroupLabel>Administration</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {adminMenuItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={pathname.startsWith(item.url)}>
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/papers/new">
                    <Plus />
                    <span>New Paper</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="p-2 space-y-2">
          {/* User Authentication Section */}
          {!isLoading && (
            <div className="mb-2">
              {isAuthenticated ? (
                <UserMenu />
              ) : (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setAuthModalOpen(true)}
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </Button>
              )}
            </div>
          )}

          {/* Theme Controls */}
          <div className="flex gap-2">
            <ThemeToggle />
          </div>

          {/* Ko-fi Support Button */}
          <KofiSupport
            variant="outline"
            size="sm"
            className="w-full justify-start"
          />
        </div>
      </SidebarFooter>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </Sidebar>
  )
}
