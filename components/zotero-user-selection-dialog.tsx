"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { authenticatedFetch } from "@/lib/utils"
import { Loader2, AlertTriangle, Library, Users, Plus } from "lucide-react"

interface ZoteroGroup {
  id: string
  name: string
  type: string
}

interface ZoteroUserSelectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paperId: string
  paperTitle: string
  onSyncSuccess?: (result: { itemKey: string; noteKey: string; message: string }) => void
  onSyncError?: (error: string) => void
}

/**
 * Dialog for user selection when paper is not found in default group
 * Implements the new simplified Zotero sync flow
 */
export function ZoteroUserSelectionDialog({
  open,
  onOpenChange,
  paperId,
  paperTitle,
  onSyncSuccess,
  onSyncError
}: ZoteroUserSelectionDialogProps) {
  const [selectedAction, setSelectedAction] = useState<'create-default' | 'select-group' | null>(null)
  const [selectedGroupId, setSelectedGroupId] = useState<string>('')
  const [availableGroups, setAvailableGroups] = useState<ZoteroGroup[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingGroups, setIsLoadingGroups] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedAction(null)
      setSelectedGroupId('')
      setError(null)
      fetchAvailableGroups()
    }
  }, [open])

  const fetchAvailableGroups = async () => {
    setIsLoadingGroups(true)
    try {
      const response = await authenticatedFetch('/api/zotero/groups')
      if (response.ok) {
        const data = await response.json()
        setAvailableGroups(data.data || [])
      } else {
        console.warn('Failed to fetch Zotero groups')
        setAvailableGroups([])
      }
    } catch (error) {
      console.error('Error fetching Zotero groups:', error)
      setAvailableGroups([])
    } finally {
      setIsLoadingGroups(false)
    }
  }

  const handleCreateInDefaultGroup = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/create-in-default-group`, {
        method: 'POST'
      })

      const data = await response.json()

      if (response.ok) {
        onSyncSuccess?.(data.data)
        onOpenChange(false)
      } else {
        const errorMessage = data.errors?.[0] || data.error || 'Failed to create in default group'
        setError(errorMessage)
        onSyncError?.(errorMessage)
      }
    } catch (error) {
      const errorMessage = 'Network error occurred while creating in default group'
      setError(errorMessage)
      onSyncError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearchOrCreateInSelectedGroup = async () => {
    if (!selectedGroupId) {
      setError('Please select a group')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // First try to search and sync in the selected group
      const searchResponse = await authenticatedFetch(`/api/papers/${paperId}/search-in-group`, {
        method: 'POST',
        body: JSON.stringify({
          libraryType: 'group',
          libraryId: selectedGroupId
        })
      })

      const searchData = await searchResponse.json()

      if (searchResponse.ok) {
        // Found existing item in selected group
        onSyncSuccess?.(searchData.data)
        onOpenChange(false)
        return
      }

      // If not found (404), try to create in the selected group
      if (searchResponse.status === 404) {
        const createResponse = await authenticatedFetch(`/api/papers/${paperId}/create-in-group`, {
          method: 'POST',
          body: JSON.stringify({
            libraryType: 'group',
            libraryId: selectedGroupId
          })
        })

        const createData = await createResponse.json()

        if (createResponse.ok) {
          onSyncSuccess?.(createData.data)
          onOpenChange(false)
        } else {
          const errorMessage = createData.errors?.[0] || createData.error || 'Failed to create in selected group'
          setError(errorMessage)
          onSyncError?.(errorMessage)
        }
      } else {
        // Other error during search
        const errorMessage = searchData.errors?.[0] || searchData.error || 'Failed to search in selected group'
        setError(errorMessage)
        onSyncError?.(errorMessage)
      }
    } catch (error) {
      const errorMessage = 'Network error occurred while syncing to selected group'
      setError(errorMessage)
      onSyncError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleProceed = () => {
    if (selectedAction === 'create-default') {
      handleCreateInDefaultGroup()
    } else if (selectedAction === 'select-group') {
      handleSearchOrCreateInSelectedGroup()
    }
  }

  const truncatedTitle = paperTitle.length > 60 
    ? `${paperTitle.substring(0, 60)}...` 
    : paperTitle

  const canProceed = selectedAction === 'create-default' || 
    (selectedAction === 'select-group' && selectedGroupId)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Paper Not Found in Default Group
          </DialogTitle>
          <DialogDescription>
            "{truncatedTitle}" was not found in your default Zotero library. 
            What would you like to do?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertDescription>
              The paper couldn't be found in your default Zotero library. You can create it there 
              or choose a different group to create it in.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <Label className="text-sm font-medium">Choose an action:</Label>
            
            <div className="space-y-2">
              <Button
                variant={selectedAction === 'create-default' ? 'default' : 'outline'}
                className="w-full justify-start"
                onClick={() => setSelectedAction('create-default')}
                disabled={isLoading}
              >
                <Plus className="mr-2 h-4 w-4" />
                <Library className="mr-2 h-4 w-4 text-blue-500" />
                Create in Default Group
              </Button>

              <Button
                variant={selectedAction === 'select-group' ? 'default' : 'outline'}
                className="w-full justify-start"
                onClick={() => setSelectedAction('select-group')}
                disabled={isLoading || isLoadingGroups}
              >
                <Users className="mr-2 h-4 w-4 text-green-500" />
                Select Another Group
              </Button>
            </div>

            {selectedAction === 'select-group' && (
              <div className="mt-3 space-y-2">
                <Label htmlFor="group-select" className="text-sm">
                  Select Zotero Group:
                </Label>
                <Select
                  value={selectedGroupId}
                  onValueChange={setSelectedGroupId}
                  disabled={isLoading || isLoadingGroups}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a group..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableGroups.map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-green-500" />
                          <span>{group.name}</span>
                          <span className="text-xs text-muted-foreground">({group.type})</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {isLoadingGroups && (
                  <p className="text-sm text-muted-foreground">Loading groups...</p>
                )}
                {!isLoadingGroups && availableGroups.length === 0 && (
                  <p className="text-sm text-muted-foreground">No groups available</p>
                )}
              </div>
            )}
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleProceed}
            disabled={isLoading || !canProceed}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {selectedAction === 'create-default' ? 'Create in Default Group' : 'Search or Create in Group'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
