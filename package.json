{"name": "my-v0-project", "version": "1.3.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "start": "npm run migrate && next start", "migrate": "npx tsx scripts/startup.ts", "validate:schema": "npx tsx scripts/validate-schema.ts", "validate:schema:quick": "npx tsx scripts/validate-schema.ts --quick", "test": "npx tsx scripts/run-tests.ts", "test:unit": "find tests/unit -name '*.test.ts' -exec node --test {} +", "test:integration": "find tests/integration -name '*.test.ts' -exec node --test {} +", "test:ci": "npm run test:unit && npm run test:integration", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:coverage": "jest --coverage", "test:jest:e2e": "jest --testPathPattern=e2e", "test:jest:unit": "jest --testPathIgnorePatterns=e2e", "test:jest:ci": "jest --coverage --watchAll=false --passWithNoTests", "health": "npx tsx scripts/health-check.ts", "health:detailed": "npx tsx scripts/health-check.ts --detailed", "email:test": "npx tsx scripts/test-email-system.ts", "email:test-send": "npx tsx scripts/test-email-system.ts --send-test-email --test-email=<EMAIL>", "validate": "npx tsx scripts/validate-acceptance-criteria.ts", "validate:quick": "npx tsx scripts/validate-acceptance-criteria.ts --skip-clone", "check-syntax": "npx tsx scripts/check-syntax.ts", "check-syntax:shell": "./scripts/check-syntax.sh", "quality": "npx tsx scripts/code-quality.ts", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "audit": "npm audit", "audit:fix": "npm audit fix", "prebuild": "echo 'Skipping quality checks for Dock<PERSON> build'", "seed:test-users": "npx tsx scripts/seed-test-users.ts", "docker:up": "docker-compose up -d", "docker:build": "docker-compose up --build -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f app"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.12", "@radix-ui/react-context-menu": "2.2.16", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.10", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto-js": "^4.2.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "express-rate-limit": "^8.0.1", "geist": "^1.3.1", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.539.0", "next": "15.2.4", "next-themes": "latest", "nodemailer": "^7.0.5", "pg": "^8.11.3", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.4", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.10.9", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}